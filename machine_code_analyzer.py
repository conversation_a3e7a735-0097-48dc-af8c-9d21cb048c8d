#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码分析工具
用于诊断机器码变动问题，特别是多硬盘环境下的稳定性问题
"""

import uuid
import hashlib
import json
import os
import platform
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

class MachineCodeAnalyzer:
    def __init__(self):
        self.salt_key = "MFChen_Video_Tool_2025_Secret_Key_MDZNB&*"
        self.analysis_results = {}
        
    def get_detailed_hardware_info(self):
        """获取详细的硬件信息用于分析"""
        info = {
            "timestamp": datetime.now().isoformat(),
            "platform": platform.platform(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
        }
        
        if os.name == "nt":  # Windows
            info.update(self._get_windows_hardware_info())
        elif os.name == "posix":  # Linux/Mac
            info.update(self._get_posix_hardware_info())
            
        # 获取MAC地址
        try:
            mac = uuid.getnode()
            info["mac_address"] = f"{mac:012x}"
            info["mac_formatted"] = ':'.join([f"{mac:012x}"[i:i+2] for i in range(0, 12, 2)])
        except:
            info["mac_address"] = "unknown"
            info["mac_formatted"] = "unknown"
            
        return info
    
    def _get_windows_hardware_info(self):
        """获取Windows系统的详细硬件信息"""
        info = {}
        
        # 主板信息
        try:
            result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber,manufacturer,product'],
                                  capture_output=True, text=True, timeout=15,
                                  creationflags=CREATE_NO_WINDOW)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info["motherboard_raw"] = lines
                info["motherboard_info"] = self._parse_wmic_output(lines)
        except Exception as e:
            info["motherboard_error"] = str(e)
        
        # CPU信息
        try:
            result = subprocess.run(['wmic', 'cpu', 'get', 'processorid,name,manufacturer'],
                                  capture_output=True, text=True, timeout=15,
                                  creationflags=CREATE_NO_WINDOW)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info["cpu_raw"] = lines
                info["cpu_info"] = self._parse_wmic_output(lines)
        except Exception as e:
            info["cpu_error"] = str(e)
        
        # 硬盘信息（详细）
        try:
            result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber,model,size,interfacetype'],
                                  capture_output=True, text=True, timeout=15,
                                  creationflags=CREATE_NO_WINDOW)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info["disk_raw"] = lines
                info["disk_info"] = self._parse_wmic_output(lines)
                info["disk_count"] = len([line for line in lines if line.strip() and 'SerialNumber' not in line])
        except Exception as e:
            info["disk_error"] = str(e)
        
        # 内存信息
        try:
            result = subprocess.run(['wmic', 'memorychip', 'get', 'serialnumber,capacity,manufacturer'],
                                  capture_output=True, text=True, timeout=15,
                                  creationflags=CREATE_NO_WINDOW)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info["memory_raw"] = lines
                info["memory_info"] = self._parse_wmic_output(lines)
        except Exception as e:
            info["memory_error"] = str(e)
            
        # BIOS信息
        try:
            result = subprocess.run(['wmic', 'bios', 'get', 'serialnumber,manufacturer,version'],
                                  capture_output=True, text=True, timeout=15,
                                  creationflags=CREATE_NO_WINDOW)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info["bios_raw"] = lines
                info["bios_info"] = self._parse_wmic_output(lines)
        except Exception as e:
            info["bios_error"] = str(e)
            
        return info
    
    def _get_posix_hardware_info(self):
        """获取Linux/Mac系统的详细硬件信息"""
        info = {}
        
        # 机器ID
        for path in ['/etc/machine-id', '/var/lib/dbus/machine-id']:
            try:
                if os.path.exists(path):
                    with open(path, 'r') as f:
                        info[f"machine_id_{path.split('/')[-1]}"] = f.read().strip()
            except Exception as e:
                info[f"machine_id_error_{path}"] = str(e)
        
        # CPU信息
        try:
            if os.path.exists('/proc/cpuinfo'):
                with open('/proc/cpuinfo', 'r') as f:
                    cpuinfo = f.read()
                    info["cpuinfo_raw"] = cpuinfo[:1000]  # 限制长度
                    # 提取关键信息
                    for line in cpuinfo.split('\n'):
                        if 'serial' in line.lower():
                            info["cpu_serial"] = line.split(':')[1].strip() if ':' in line else line
                            break
        except Exception as e:
            info["cpuinfo_error"] = str(e)
            
        return info
    
    def _parse_wmic_output(self, lines):
        """解析WMIC命令的输出"""
        parsed = []
        if len(lines) < 2:
            return parsed
            
        headers = [h.strip() for h in lines[0].split()]
        for line in lines[1:]:
            if line.strip():
                values = [v.strip() for v in line.split()]
                if values:
                    parsed.append(dict(zip(headers, values)))
        return parsed

    def generate_machine_code_components(self):
        """生成机器码的各个组件，用于分析哪个组件可能导致变动"""
        components = {}

        if os.name == "nt":  # Windows
            # 主板序列号
            try:
                result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            components["motherboard_serial"] = line.strip()
                            break
            except:
                components["motherboard_serial"] = "unknown"

            # CPU ID
            try:
                result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            components["cpu_id"] = line.strip()
                            break
            except:
                components["cpu_id"] = "unknown"

            # 硬盘序列号（所有硬盘）
            try:
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber,model,size'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    disk_info = []
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.strip().split()
                            if parts:
                                disk_info.append({
                                    "serial": parts[0] if len(parts) > 0 else "unknown",
                                    "model": parts[1] if len(parts) > 1 else "unknown",
                                    "size": parts[2] if len(parts) > 2 else "unknown"
                                })
                    components["disk_info"] = disk_info
                    components["disk_serials"] = [d["serial"] for d in disk_info]
                    components["first_disk_serial"] = disk_info[0]["serial"] if disk_info else "unknown"
                    components["disk_count"] = len(disk_info)
            except:
                components["disk_info"] = []
                components["disk_serials"] = []
                components["first_disk_serial"] = "unknown"
                components["disk_count"] = 0

        else:  # Linux/Mac
            # 机器ID
            for path in ['/etc/machine-id', '/var/lib/dbus/machine-id']:
                try:
                    if os.path.exists(path):
                        with open(path, 'r') as f:
                            components[f"machine_id_{path.split('/')[-1]}"] = f.read().strip()
                except:
                    pass

        # MAC地址
        try:
            mac = uuid.getnode()
            components["mac_address"] = f"{mac:012x}"
        except:
            components["mac_address"] = "unknown"

        return components

    def generate_machine_code_original(self):
        """使用原始算法生成机器码"""
        if os.name == "nt":  # Windows
            try:
                # 主板序列号
                result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                motherboard_serial = ""
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            motherboard_serial = line.strip()
                            break

                # CPU ID
                result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                cpu_id = ""
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            cpu_id = line.strip()
                            break

                # 第一个硬盘序列号
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                disk_serial = ""
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            disk_serial = line.strip()
                            break

                # 组合硬件信息
                hardware_info = f"{motherboard_serial}_{cpu_id}_{disk_serial}"

            except Exception:
                # 如果获取硬件信息失败，使用MAC地址作为备用
                mac = uuid.getnode()
                hardware_info = f"mac_{mac:012x}"

        else:  # Linux/Mac
            try:
                # 尝试读取机器ID
                machine_id = ""
                for path in ['/etc/machine-id', '/var/lib/dbus/machine-id']:
                    try:
                        if os.path.exists(path):
                            with open(path, 'r') as f:
                                machine_id = f.read().strip()
                                break
                    except:
                        continue

                if machine_id:
                    hardware_info = f"machine_id_{machine_id}"
                else:
                    # 如果没有机器ID，使用MAC地址
                    mac = uuid.getnode()
                    hardware_info = f"mac_{mac:012x}"

            except Exception:
                # 最后的备用方案
                mac = uuid.getnode()
                hardware_info = f"mac_{mac:012x}"

        # 生成机器码
        machine_code = hashlib.md5(hardware_info.encode()).hexdigest()
        return machine_code, hardware_info

    def convert_to_md5_with_salt(self, machine_code):
        """使用盐值转换机器码为MD5"""
        combined = f"{machine_code}_{self.salt_key}_{machine_code[:8]}"
        md5_hash = hashlib.md5(combined.encode()).hexdigest()
        return md5_hash.upper()

    def run_analysis(self, iterations=5, interval=2):
        """运行多次分析，检测机器码稳定性"""
        print("🔍 开始机器码稳定性分析...")
        print(f"将进行 {iterations} 次检测，间隔 {interval} 秒")
        print("=" * 60)

        results = []

        for i in range(iterations):
            print(f"\n📊 第 {i+1}/{iterations} 次检测:")

            # 获取详细硬件信息
            hardware_info = self.get_detailed_hardware_info()

            # 获取机器码组件
            components = self.generate_machine_code_components()

            # 生成机器码
            machine_code, hardware_string = self.generate_machine_code_original()

            # 转换为MD5
            md5_code = self.convert_to_md5_with_salt(machine_code)

            result = {
                "iteration": i + 1,
                "timestamp": datetime.now().isoformat(),
                "machine_code": machine_code,
                "md5_code": md5_code,
                "hardware_string": hardware_string,
                "components": components,
                "hardware_info": hardware_info
            }

            results.append(result)

            print(f"  机器码: {machine_code}")
            print(f"  MD5码: {md5_code}")
            print(f"  硬件字符串: {hardware_string}")
            print(f"  硬盘数量: {components.get('disk_count', 0)}")
            print(f"  第一个硬盘: {components.get('first_disk_serial', 'unknown')}")

            if i < iterations - 1:
                print(f"  等待 {interval} 秒...")
                time.sleep(interval)

        return results

    def analyze_results(self, results):
        """分析检测结果"""
        print("\n" + "=" * 60)
        print("📈 分析结果:")

        # 检查机器码稳定性
        machine_codes = [r["machine_code"] for r in results]
        md5_codes = [r["md5_code"] for r in results]

        unique_machine_codes = set(machine_codes)
        unique_md5_codes = set(md5_codes)

        print(f"\n🔑 机器码稳定性:")
        print(f"  检测次数: {len(results)}")
        print(f"  唯一机器码数量: {len(unique_machine_codes)}")
        print(f"  唯一MD5码数量: {len(unique_md5_codes)}")

        if len(unique_machine_codes) == 1:
            print("  ✅ 机器码完全稳定")
        else:
            print("  ❌ 机器码不稳定，发现变动！")
            print("  变动的机器码:")
            for i, code in enumerate(unique_machine_codes):
                count = machine_codes.count(code)
                print(f"    {i+1}. {code} (出现{count}次)")

        # 分析硬盘信息
        print(f"\n💾 硬盘信息分析:")
        disk_counts = [r["components"].get("disk_count", 0) for r in results]
        first_disks = [r["components"].get("first_disk_serial", "unknown") for r in results]

        unique_disk_counts = set(disk_counts)
        unique_first_disks = set(first_disks)

        print(f"  硬盘数量变化: {unique_disk_counts}")
        print(f"  第一个硬盘序列号变化: {len(unique_first_disks)} 种")

        if len(unique_first_disks) > 1:
            print("  ⚠️  第一个硬盘序列号发生变化:")
            for i, disk in enumerate(unique_first_disks):
                count = first_disks.count(disk)
                print(f"    {i+1}. {disk} (出现{count}次)")

        # 分析硬件组件稳定性
        print(f"\n🔧 硬件组件稳定性:")

        # 主板序列号
        motherboard_serials = [r["components"].get("motherboard_serial", "unknown") for r in results]
        unique_motherboard = set(motherboard_serials)
        print(f"  主板序列号: {'稳定' if len(unique_motherboard) == 1 else '不稳定'} ({len(unique_motherboard)} 种)")

        # CPU ID
        cpu_ids = [r["components"].get("cpu_id", "unknown") for r in results]
        unique_cpu = set(cpu_ids)
        print(f"  CPU ID: {'稳定' if len(unique_cpu) == 1 else '不稳定'} ({len(unique_cpu)} 种)")

        # MAC地址
        mac_addresses = [r["components"].get("mac_address", "unknown") for r in results]
        unique_mac = set(mac_addresses)
        print(f"  MAC地址: {'稳定' if len(unique_mac) == 1 else '不稳定'} ({len(unique_mac)} 种)")

        # 不在这里保存报告，避免递归调用

        return {
            "stable": len(unique_machine_codes) == 1,
            "machine_codes": list(unique_machine_codes),
            "md5_codes": list(unique_md5_codes),
            "disk_stability": len(unique_first_disks) == 1,
            "component_stability": {
                "motherboard": len(unique_motherboard) == 1,
                "cpu": len(unique_cpu) == 1,
                "mac": len(unique_mac) == 1
            }
        }

    def save_report(self, results):
        """保存详细报告到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"machine_code_analysis_{timestamp}.json"

        report = {
            "analysis_time": datetime.now().isoformat(),
            "system_info": {
                "platform": platform.platform(),
                "system": platform.system(),
                "machine": platform.machine(),
                "processor": platform.processor()
            },
            "results": results,
            "summary": "Analysis completed - see results for details"
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n📄 详细报告已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存报告失败: {e}")


def main():
    """主函数"""
    print("🔍 机器码稳定性分析工具")
    print("用于诊断机器码变动问题，特别是多硬盘环境")
    print("=" * 60)

    analyzer = MachineCodeAnalyzer()

    # 显示当前系统信息
    print(f"系统: {platform.system()} {platform.release()}")
    print(f"平台: {platform.platform()}")
    print(f"处理器: {platform.processor()}")

    try:
        # 运行分析
        results = analyzer.run_analysis(iterations=5, interval=3)

        # 分析结果
        summary = analyzer.analyze_results(results)

        # 给出建议
        print("\n" + "=" * 60)
        print("💡 建议:")

        if summary["stable"]:
            print("  ✅ 机器码稳定，无需担心")
        else:
            print("  ⚠️  机器码不稳定，可能的原因:")
            if not summary["disk_stability"]:
                print("    - 硬盘顺序或序列号发生变化")
                print("    - 建议检查硬盘连接和BIOS设置")
            if not summary["component_stability"]["motherboard"]:
                print("    - 主板序列号变化")
            if not summary["component_stability"]["cpu"]:
                print("    - CPU ID变化")
            if not summary["component_stability"]["mac"]:
                print("    - MAC地址变化")

        print("\n🔧 如果机器码不稳定，建议:")
        print("  1. 检查硬盘连接是否松动")
        print("  2. 检查BIOS中硬盘启动顺序设置")
        print("  3. 避免频繁插拔USB设备")
        print("  4. 检查是否有虚拟硬盘或网络硬盘")

    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
