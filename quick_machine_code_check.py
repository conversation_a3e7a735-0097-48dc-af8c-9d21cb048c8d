#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速机器码检查工具
用于快速检查当前机器的机器码信息和稳定性
"""

import os
import sys
import time
from datetime import datetime
from machine_code_analyzer import MachineCodeAnalyzer

def print_separator(title="", char="=", width=60):
    """打印分隔线"""
    if title:
        title_line = f" {title} "
        padding = (width - len(title_line)) // 2
        line = char * padding + title_line + char * padding
        if len(line) < width:
            line += char
    else:
        line = char * width
    print(line)

def print_status(status, message):
    """打印状态信息"""
    status_icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌",
        "question": "❓"
    }
    icon = status_icons.get(status, "•")
    print(f"{icon} {message}")

def format_hardware_info(components):
    """格式化硬件信息"""
    info = []
    info.append(f"主板序列号: {components.get('motherboard_serial', 'Unknown')}")
    info.append(f"CPU ID: {components.get('cpu_id', 'Unknown')}")
    info.append(f"MAC地址: {components.get('mac_address', 'Unknown')}")
    info.append(f"硬盘数量: {components.get('disk_count', 0)}")
    
    disk_info = components.get('disk_info', [])
    if disk_info:
        info.append("硬盘详情:")
        for i, disk in enumerate(disk_info, 1):
            serial = disk.get('serial', 'Unknown')
            model = disk.get('model', 'Unknown')
            size = disk.get('size', 'Unknown')
            info.append(f"  {i}. {serial} ({model}, {size})")
    
    return info

def quick_check():
    """快速检查当前机器码"""
    print_separator("机器码快速检查工具")
    print_status("info", "正在获取机器硬件信息...")
    
    try:
        analyzer = MachineCodeAnalyzer()
        
        # 获取当前机器码信息
        components = analyzer.generate_machine_code_components()
        machine_code, hardware_string = analyzer.generate_machine_code_original()
        md5_code = analyzer.convert_to_md5_with_salt(machine_code)
        
        print_separator("当前机器信息")
        print(f"机器码: {machine_code}")
        print(f"MD5码: {md5_code}")
        print(f"硬件字符串: {hardware_string}")
        print()
        
        print_separator("硬件组件详情")
        hardware_info = format_hardware_info(components)
        for info in hardware_info:
            print(info)
        print()
        
        # 检查是否在授权列表中
        print_separator("授权状态检查")
        try:
            from machine_code_verifier import MachineCodeVerifier
            verifier = MachineCodeVerifier()
            is_authorized = verifier.is_machine_authorized()
            current_md5 = verifier.get_machine_md5()
            
            if is_authorized:
                print_status("success", "当前机器已授权")
                print(f"授权MD5: {current_md5}")
            else:
                print_status("warning", "当前机器未授权")
                print(f"当前MD5: {current_md5}")
                print_status("info", "请联系管理员添加授权")
        except ImportError:
            print_status("warning", "无法检查授权状态（缺少授权模块）")
        
        print()
        return True
        
    except Exception as e:
        print_status("error", f"检查失败: {str(e)}")
        return False

def stability_test(iterations=3, interval=2):
    """稳定性测试"""
    print_separator("机器码稳定性测试")
    print_status("info", f"将进行 {iterations} 次检测，间隔 {interval} 秒")
    print()
    
    try:
        analyzer = MachineCodeAnalyzer()
        results = []
        
        for i in range(iterations):
            print(f"📊 第 {i+1}/{iterations} 次检测:")
            
            # 获取机器码信息
            components = analyzer.generate_machine_code_components()
            machine_code, _ = analyzer.generate_machine_code_original()
            md5_code = analyzer.convert_to_md5_with_salt(machine_code)
            
            result = {
                "iteration": i + 1,
                "machine_code": machine_code,
                "md5_code": md5_code,
                "disk_count": components.get('disk_count', 0),
                "first_disk": components.get('first_disk_serial', 'unknown'),
                "timestamp": datetime.now()
            }
            results.append(result)
            
            print(f"  机器码: {machine_code}")
            print(f"  MD5码: {md5_code}")
            print(f"  硬盘数量: {result['disk_count']}")
            print(f"  第一硬盘: {result['first_disk']}")
            
            if i < iterations - 1:
                print(f"  等待 {interval} 秒...")
                time.sleep(interval)
            print()
        
        # 分析结果
        print_separator("稳定性分析结果")
        
        machine_codes = [r["machine_code"] for r in results]
        md5_codes = [r["md5_code"] for r in results]
        first_disks = [r["first_disk"] for r in results]
        disk_counts = [r["disk_count"] for r in results]
        
        unique_machine_codes = set(machine_codes)
        unique_md5_codes = set(md5_codes)
        unique_first_disks = set(first_disks)
        unique_disk_counts = set(disk_counts)
        
        print(f"检测次数: {len(results)}")
        print(f"唯一机器码数量: {len(unique_machine_codes)}")
        print(f"唯一MD5码数量: {len(unique_md5_codes)}")
        print(f"硬盘数量变化: {len(unique_disk_counts)} 种")
        print(f"第一硬盘变化: {len(unique_first_disks)} 种")
        print()
        
        # 判断稳定性
        if len(unique_machine_codes) == 1:
            print_status("success", "机器码完全稳定")
            print_status("info", "无需担心机器码变动问题")
        else:
            print_status("error", "检测到机器码不稳定！")
            print_status("warning", "发现以下变动:")
            
            if len(unique_machine_codes) > 1:
                print("  • 机器码发生变化:")
                for code in unique_machine_codes:
                    count = machine_codes.count(code)
                    print(f"    - {code} (出现{count}次)")
            
            if len(unique_first_disks) > 1:
                print("  • 第一硬盘序列号发生变化:")
                for disk in unique_first_disks:
                    count = first_disks.count(disk)
                    print(f"    - {disk} (出现{count}次)")
            
            if len(unique_disk_counts) > 1:
                print("  • 硬盘数量发生变化:")
                for count in unique_disk_counts:
                    occurrences = disk_counts.count(count)
                    print(f"    - {count}个硬盘 (出现{occurrences}次)")
            
            print()
            print_separator("建议解决方案")
            print_status("info", "1. 检查硬盘连接是否松动")
            print_status("info", "2. 固定BIOS中硬盘启动顺序")
            print_status("info", "3. 避免频繁插拔USB设备")
            print_status("info", "4. 检查是否有虚拟硬盘影响")
            print_status("info", "5. 重启后再次测试确认问题")
        
        print()
        return len(unique_machine_codes) == 1
        
    except Exception as e:
        print_status("error", f"稳定性测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 机器码诊断工具")
    print("用于快速检查机器码信息和稳定性")
    print()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--stability" or sys.argv[1] == "-s":
            # 稳定性测试模式
            iterations = 5
            interval = 2
            
            if len(sys.argv) > 2:
                try:
                    iterations = int(sys.argv[2])
                except ValueError:
                    print_status("warning", "无效的检测次数，使用默认值5")
            
            if len(sys.argv) > 3:
                try:
                    interval = int(sys.argv[3])
                except ValueError:
                    print_status("warning", "无效的间隔时间，使用默认值2秒")
            
            stability_test(iterations, interval)
        elif sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("使用方法:")
            print("  python quick_machine_code_check.py           # 快速检查")
            print("  python quick_machine_code_check.py -s [次数] [间隔]  # 稳定性测试")
            print("  python quick_machine_code_check.py --help    # 显示帮助")
            print()
            print("示例:")
            print("  python quick_machine_code_check.py -s 10 3   # 检测10次，间隔3秒")
        else:
            print_status("error", f"未知参数: {sys.argv[1]}")
            print_status("info", "使用 --help 查看帮助")
    else:
        # 默认快速检查模式
        quick_check()
        
        print()
        print_status("question", "是否进行稳定性测试？(y/n): ")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', '是']:
                print()
                stability_test()
        except KeyboardInterrupt:
            print()
            print_status("info", "用户取消操作")

if __name__ == "__main__":
    main()
