#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码分析工具 - GUI版本
用于直观分析机器码变动问题，特别适合诊断多硬盘环境下的稳定性问题
"""

import sys
import json
import os
from datetime import datetime
from pathlib import Path

try:
    from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                   QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                                   QProgressBar, QGroupBox, QGridLayout, QSpinBox,
                                   QMessageBox, QFileDialog, QTabWidget, QTableWidget,
                                   QTableWidgetItem, QHeaderView, QSplitter)
    from PySide6.QtCore import QThread, Signal, QTimer, Qt
    from PySide6.QtGui import QFont, QColor, QPalette
except ImportError:
    print("错误：需要安装PySide6")
    print("请运行：pip install PySide6")
    sys.exit(1)

from machine_code_analyzer import MachineCodeAnalyzer

class AnalysisWorker(QThread):
    """分析工作线程"""
    progress_updated = Signal(int, int)  # current, total
    result_updated = Signal(dict)  # single result
    analysis_completed = Signal(list)  # all results
    log_updated = Signal(str)
    
    def __init__(self, iterations=5, interval=2):
        super().__init__()
        self.iterations = iterations
        self.interval = interval
        self.analyzer = MachineCodeAnalyzer()
        self.is_running = False
    
    def stop(self):
        self.is_running = False
    
    def run(self):
        self.is_running = True
        self.log_updated.emit("🔍 开始机器码稳定性分析...")
        self.log_updated.emit(f"将进行 {self.iterations} 次检测，间隔 {self.interval} 秒")
        
        results = []
        
        for i in range(self.iterations):
            if not self.is_running:
                break
                
            self.log_updated.emit(f"\n📊 第 {i+1}/{self.iterations} 次检测:")
            
            try:
                # 获取详细硬件信息
                hardware_info = self.analyzer.get_detailed_hardware_info()
                
                # 获取机器码组件
                components = self.analyzer.generate_machine_code_components()
                
                # 生成机器码
                machine_code, hardware_string = self.analyzer.generate_machine_code_original()
                
                # 转换为MD5
                md5_code = self.analyzer.convert_to_md5_with_salt(machine_code)
                
                result = {
                    "iteration": i + 1,
                    "timestamp": datetime.now().isoformat(),
                    "machine_code": machine_code,
                    "md5_code": md5_code,
                    "hardware_string": hardware_string,
                    "components": components,
                    "hardware_info": hardware_info
                }
                
                results.append(result)
                
                self.log_updated.emit(f"  机器码: {machine_code}")
                self.log_updated.emit(f"  MD5码: {md5_code}")
                self.log_updated.emit(f"  硬盘数量: {components.get('disk_count', 0)}")
                self.log_updated.emit(f"  第一个硬盘: {components.get('first_disk_serial', 'unknown')}")
                
                # 发送单个结果
                self.result_updated.emit(result)
                
                # 更新进度
                self.progress_updated.emit(i + 1, self.iterations)
                
                if i < self.iterations - 1:
                    self.log_updated.emit(f"  等待 {self.interval} 秒...")
                    self.msleep(self.interval * 1000)  # 转换为毫秒
                    
            except Exception as e:
                self.log_updated.emit(f"❌ 检测失败: {str(e)}")
        
        if self.is_running:
            self.analysis_completed.emit(results)
            self.log_updated.emit("\n✅ 分析完成！")

class MachineCodeGUIAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.results = []
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("机器码稳定性分析工具 - GUI版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("🔍 机器码稳定性分析工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 创建控制面板
        control_group = QGroupBox("分析控制")
        control_layout = QHBoxLayout(control_group)
        
        # 检测次数
        control_layout.addWidget(QLabel("检测次数:"))
        self.iterations_spinbox = QSpinBox()
        self.iterations_spinbox.setRange(1, 20)
        self.iterations_spinbox.setValue(5)
        control_layout.addWidget(self.iterations_spinbox)
        
        # 间隔时间
        control_layout.addWidget(QLabel("间隔(秒):"))
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(1, 60)
        self.interval_spinbox.setValue(3)
        control_layout.addWidget(self.interval_spinbox)
        
        # 开始按钮
        self.start_button = QPushButton("🚀 开始分析")
        self.start_button.clicked.connect(self.start_analysis)
        control_layout.addWidget(self.start_button)
        
        # 停止按钮
        self.stop_button = QPushButton("⏹️ 停止分析")
        self.stop_button.clicked.connect(self.stop_analysis)
        self.stop_button.setEnabled(False)
        control_layout.addWidget(self.stop_button)
        
        # 保存报告按钮
        self.save_button = QPushButton("💾 保存报告")
        self.save_button.clicked.connect(self.save_report)
        self.save_button.setEnabled(False)
        control_layout.addWidget(self.save_button)
        
        control_layout.addStretch()
        main_layout.addWidget(control_group)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        main_layout.addWidget(self.progress_bar)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        splitter.addWidget(self.tab_widget)
        
        # 日志标签页
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.tab_widget.addTab(self.log_text, "📋 分析日志")
        
        # 结果表格标签页
        self.results_table = QTableWidget()
        self.setup_results_table()
        self.tab_widget.addTab(self.results_table, "📊 检测结果")
        
        # 硬件信息标签页
        self.hardware_text = QTextEdit()
        self.hardware_text.setFont(QFont("Consolas", 9))
        self.tab_widget.addTab(self.hardware_text, "🔧 硬件信息")
        
        # 创建分析结果面板
        self.analysis_panel = self.create_analysis_panel()
        splitter.addWidget(self.analysis_panel)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
        
    def setup_results_table(self):
        """设置结果表格"""
        headers = ["检测次数", "机器码", "MD5码", "硬盘数量", "第一硬盘", "时间"]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
    def create_analysis_panel(self):
        """创建分析结果面板"""
        panel = QGroupBox("📈 分析结果")
        layout = QVBoxLayout(panel)
        
        # 稳定性状态
        self.stability_label = QLabel("等待分析...")
        self.stability_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.stability_label)
        
        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout(stats_group)
        
        stats_layout.addWidget(QLabel("检测次数:"), 0, 0)
        self.total_tests_label = QLabel("0")
        stats_layout.addWidget(self.total_tests_label, 0, 1)
        
        stats_layout.addWidget(QLabel("唯一机器码:"), 1, 0)
        self.unique_codes_label = QLabel("0")
        stats_layout.addWidget(self.unique_codes_label, 1, 1)
        
        stats_layout.addWidget(QLabel("硬盘数量:"), 2, 0)
        self.disk_count_label = QLabel("0")
        stats_layout.addWidget(self.disk_count_label, 2, 1)
        
        stats_layout.addWidget(QLabel("第一硬盘变化:"), 3, 0)
        self.first_disk_changes_label = QLabel("0")
        stats_layout.addWidget(self.first_disk_changes_label, 3, 1)
        
        layout.addWidget(stats_group)
        
        # 建议信息
        self.suggestions_text = QTextEdit()
        self.suggestions_text.setMaximumHeight(150)
        self.suggestions_text.setPlainText("等待分析完成后显示建议...")
        layout.addWidget(QLabel("💡 建议:"))
        layout.addWidget(self.suggestions_text)
        
        layout.addStretch()
        return panel

    def start_analysis(self):
        """开始分析"""
        if self.worker and self.worker.isRunning():
            return

        # 清空之前的结果
        self.results.clear()
        self.results_table.setRowCount(0)
        self.log_text.clear()
        self.hardware_text.clear()

        # 重置UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.save_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximum(self.iterations_spinbox.value())

        # 创建并启动工作线程
        self.worker = AnalysisWorker(
            iterations=self.iterations_spinbox.value(),
            interval=self.interval_spinbox.value()
        )

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.result_updated.connect(self.add_result)
        self.worker.analysis_completed.connect(self.analysis_finished)
        self.worker.log_updated.connect(self.add_log)

        # 启动线程
        self.worker.start()

    def stop_analysis(self):
        """停止分析"""
        if self.worker:
            self.worker.stop()
            self.worker.wait()  # 等待线程结束

        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        if self.results:
            self.save_button.setEnabled(True)

    def update_progress(self, current, total):
        """更新进度条"""
        self.progress_bar.setValue(current)

    def add_log(self, message):
        """添加日志"""
        self.log_text.append(message)

    def add_result(self, result):
        """添加单个结果到表格"""
        self.results.append(result)

        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # 添加数据到表格
        self.results_table.setItem(row, 0, QTableWidgetItem(str(result["iteration"])))
        self.results_table.setItem(row, 1, QTableWidgetItem(result["machine_code"]))
        self.results_table.setItem(row, 2, QTableWidgetItem(result["md5_code"]))
        self.results_table.setItem(row, 3, QTableWidgetItem(str(result["components"].get("disk_count", 0))))
        self.results_table.setItem(row, 4, QTableWidgetItem(result["components"].get("first_disk_serial", "unknown")))

        # 格式化时间
        timestamp = datetime.fromisoformat(result["timestamp"])
        time_str = timestamp.strftime("%H:%M:%S")
        self.results_table.setItem(row, 5, QTableWidgetItem(time_str))

        # 如果机器码发生变化，高亮显示
        if len(self.results) > 1:
            prev_code = self.results[-2]["machine_code"]
            if result["machine_code"] != prev_code:
                for col in range(self.results_table.columnCount()):
                    item = self.results_table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 200, 200))  # 红色背景

        # 滚动到最新行
        self.results_table.scrollToBottom()

    def analysis_finished(self, results):
        """分析完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.save_button.setEnabled(True)

        # 分析结果
        self.analyze_results(results)

        # 显示硬件信息
        self.show_hardware_info(results)

    def analyze_results(self, results):
        """分析结果并更新UI"""
        if not results:
            return

        # 统计信息
        machine_codes = [r["machine_code"] for r in results]
        md5_codes = [r["md5_code"] for r in results]
        first_disks = [r["components"].get("first_disk_serial", "unknown") for r in results]
        disk_counts = [r["components"].get("disk_count", 0) for r in results]

        unique_machine_codes = set(machine_codes)
        unique_md5_codes = set(md5_codes)
        unique_first_disks = set(first_disks)
        unique_disk_counts = set(disk_counts)

        # 更新统计标签
        self.total_tests_label.setText(str(len(results)))
        self.unique_codes_label.setText(str(len(unique_machine_codes)))
        self.disk_count_label.setText(str(list(unique_disk_counts)[0] if len(unique_disk_counts) == 1 else "变化"))
        self.first_disk_changes_label.setText(str(len(unique_first_disks)))

        # 判断稳定性
        is_stable = len(unique_machine_codes) == 1

        if is_stable:
            self.stability_label.setText("✅ 机器码完全稳定")
            self.stability_label.setStyleSheet("color: green; font-weight: bold;")
            suggestions = "机器码稳定，无需担心。\n\n当前配置：\n"
            suggestions += f"• 机器码: {machine_codes[0]}\n"
            suggestions += f"• MD5码: {md5_codes[0]}\n"
            suggestions += f"• 硬盘数量: {disk_counts[0]}\n"
            suggestions += f"• 第一硬盘: {first_disks[0]}"
        else:
            self.stability_label.setText("❌ 机器码不稳定")
            self.stability_label.setStyleSheet("color: red; font-weight: bold;")
            suggestions = "⚠️ 检测到机器码变动！\n\n可能原因：\n"

            if len(unique_first_disks) > 1:
                suggestions += "• 第一硬盘序列号发生变化\n"
                suggestions += f"  变化情况: {list(unique_first_disks)}\n"

            if len(unique_disk_counts) > 1:
                suggestions += "• 硬盘数量发生变化\n"
                suggestions += f"  数量变化: {list(unique_disk_counts)}\n"

            suggestions += "\n建议解决方案：\n"
            suggestions += "1. 检查硬盘连接是否松动\n"
            suggestions += "2. 固定BIOS中硬盘启动顺序\n"
            suggestions += "3. 避免频繁插拔USB设备\n"
            suggestions += "4. 检查是否有虚拟硬盘影响\n"
            suggestions += "5. 重启后再次测试确认问题"

        self.suggestions_text.setPlainText(suggestions)

    def show_hardware_info(self, results):
        """显示硬件信息"""
        if not results:
            return

        latest_result = results[-1]
        hardware_info = latest_result.get("hardware_info", {})
        components = latest_result.get("components", {})

        info_text = "=== 系统信息 ===\n"
        info_text += f"系统: {hardware_info.get('system', 'Unknown')}\n"
        info_text += f"平台: {hardware_info.get('platform', 'Unknown')}\n"
        info_text += f"处理器: {hardware_info.get('processor', 'Unknown')}\n\n"

        info_text += "=== 硬件组件 ===\n"
        info_text += f"主板序列号: {components.get('motherboard_serial', 'Unknown')}\n"
        info_text += f"CPU ID: {components.get('cpu_id', 'Unknown')}\n"
        info_text += f"MAC地址: {components.get('mac_address', 'Unknown')}\n\n"

        info_text += "=== 硬盘信息 ===\n"
        disk_info = components.get("disk_info", [])
        for i, disk in enumerate(disk_info, 1):
            info_text += f"硬盘 {i}:\n"
            info_text += f"  序列号: {disk.get('serial', 'Unknown')}\n"
            info_text += f"  型号: {disk.get('model', 'Unknown')}\n"
            info_text += f"  大小: {disk.get('size', 'Unknown')}\n\n"

        info_text += "=== 机器码生成信息 ===\n"
        info_text += f"硬件字符串: {latest_result.get('hardware_string', 'Unknown')}\n"
        info_text += f"机器码: {latest_result.get('machine_code', 'Unknown')}\n"
        info_text += f"MD5码: {latest_result.get('md5_code', 'Unknown')}\n"

        self.hardware_text.setPlainText(info_text)

    def save_report(self):
        """保存报告"""
        if not self.results:
            QMessageBox.warning(self, "警告", "没有分析结果可保存！")
            return

        # 选择保存位置
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"machine_code_analysis_{timestamp}.json"

        filename, _ = QFileDialog.getSaveFileName(
            self, "保存分析报告", default_filename, "JSON文件 (*.json)"
        )

        if filename:
            try:
                report = {
                    "analysis_time": datetime.now().isoformat(),
                    "system_info": self.results[-1].get("hardware_info", {}) if self.results else {},
                    "results": self.results,
                    "summary": {
                        "total_tests": len(self.results),
                        "unique_machine_codes": len(set(r["machine_code"] for r in self.results)),
                        "stable": len(set(r["machine_code"] for r in self.results)) == 1
                    }
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "成功", f"报告已保存到:\n{filename}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存报告失败:\n{str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("机器码稳定性分析工具")
    app.setApplicationVersion("1.0")

    # 创建主窗口
    window = MachineCodeGUIAnalyzer()
    window.show()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
