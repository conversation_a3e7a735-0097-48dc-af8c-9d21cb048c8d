import sys
import io

# 启动优化 - 必须在其他导入之前
if getattr(sys, 'frozen', False):
    # 打包环境优化
    import startup_optimizer  # 执行启动优化

    # 重定向输出到空设备
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()
else:
    # 开发环境下正常配置编码
    if sys.stdout is not None:
        sys.stdout.reconfigure(encoding='utf-8')
    if sys.stderr is not None:
        sys.stderr.reconfigure(encoding='utf-8')
import os
import re
import random
import glob
# import shlex  # 暂时未使用
# import shutil  # 暂时未使用
import subprocess

if sys.platform.startswith('win'):
    # 隐藏控制台窗口的标志
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

#from moviepy import afx, vfx, VideoFileClip, AudioFileClip
#from moviepy import concatenate_audioclips
from pathlib import Path
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QFileDialog,
    QMessageBox,
    QRadioButton,
    QVBoxLayout,
    QButtonGroup,
    # QWidget,  # 暂时未使用
    QListView,
)
from PySide6.QtCore import QStringListModel, Qt, QThread, Signal  # QUrl暂时未使用
from PySide6.QtGui import QDragEnterEvent, QDropEvent
from ui_main_window import Ui_MainWindow
from video_mixer import VideoMixer, MixingThread
from PySide6.QtCore import QSettings
from utils import sanitize_filename
from utils import get_video_metadata
# from utils import get_video_duration  # 在需要时动态导入
# from utils import get_valid_luts  # 暂时未使用
# from utils import _get_video_resolution  # 暂时未使用
# from utils import get_process_time  # 暂时未使用
# from moviepy import concatenate_audioclips  # 暂时未使用

VIDEO_BITRATE = "15000k"        # H.264码率（提高到15M，确保高质量）
GPU_ENABLED = True              # 强制开启GPU（无GPU设为False）
CPU_PRESET = "slow"             # CPU编码预设（slow/medium/fast）
GPU_PRESET = "p2"               # GPU编码预设（p0最快，p7最慢）
ENABLE_VIDEO_PREPROCESSING = True  # 启用视频预处理功能（解决编码兼容性问题）
# 定义 is_gpu_supported 函数
def is_gpu_supported():
    """检查NVIDIA GPU和CUDA支持"""
    try:
        # 检查nvidia-smi命令是否可用
        result = subprocess.run(
            ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
            capture_output=True,
            text=True,
            timeout=5,
            creationflags=CREATE_NO_WINDOW
        )
        if result.returncode == 0 and result.stdout.strip():
            return True
        else:
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
        return False

def build_gpu_ffmpeg_cmd(base_cmd, force_gpu=False):
    """
    构建GPU加速的FFmpeg命令
    :param base_cmd: 基础FFmpeg命令列表
    :param force_gpu: 是否强制使用GPU（用于测试）
    :return: 优化后的命令列表和是否使用GPU的标志
    """
    cmd = base_cmd.copy()
    use_gpu = (GPU_ENABLED and is_gpu_supported()) or force_gpu

    if use_gpu:
        # 1. 添加硬件加速解码参数（必须在第一个-i之前）
        if "-i" in cmd:
            first_i_index = cmd.index("-i")
            cmd.insert(first_i_index, "-hwaccel")
            cmd.insert(first_i_index + 1, "cuda")

        # 2. 替换视频编码器为GPU编码器
        if "-c:v" in cmd:
            codec_index = cmd.index("-c:v") + 1
            if codec_index < len(cmd):
                cmd[codec_index] = "h264_nvenc"
        else:
            # 如果没有-c:v参数，在输出文件前添加
            if len(cmd) > 0 and not cmd[-1].startswith("-"):
                cmd.insert(-1, "-c:v")
                cmd.insert(-1, "h264_nvenc")

        # 3. 添加GPU编码参数
        if "-preset" not in cmd:
            cmd.extend(["-preset", GPU_PRESET])
        if "-bf" not in cmd:
            cmd.extend(["-bf", "0"])

        return cmd, True
    else:
        # 使用CPU编码
        if "-c:v" in cmd:
            codec_index = cmd.index("-c:v") + 1
            if codec_index < len(cmd):
                cmd[codec_index] = "libx264"
        else:
            if len(cmd) > 0 and not cmd[-1].startswith("-"):
                cmd.insert(-1, "-c:v")
                cmd.insert(-1, "libx264")

        if "-preset" not in cmd:
            cmd.extend(["-preset", CPU_PRESET])
        return cmd, False


class HJMixingThread(QThread):
    """混剪处理线程"""
    progress_updated = Signal(int)
    log_updated = Signal(str)
    finished = Signal()

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.should_stop = False

    def stop(self):
        self.should_stop = True

    def run(self):
        try:
            self.process_hj_mixing()
        except Exception as e:
            self.log_updated.emit(f"❌ 混剪过程中发生错误：{str(e)[:100]}")
        finally:
            self.finished.emit()

    def process_hj_mixing(self):
        """执行混剪处理"""
        # 获取设置参数 - 如果高级设置不可见，使用默认值
        if self.main_window.ui.HJ_groupBox_GJ.isVisible():
            # 高级设置可见，使用用户设置的值
            pd_min = self.main_window.ui.GJ_spinBox_PDmin.value()
            pd_max = self.main_window.ui.GJ_spinBox_PDmax.value()
            zsc_min = self.main_window.ui.GJ_spinBox_ZSCmin.value()
            zsc_max = self.main_window.ui.GJ_spinBox_ZSCmax.value()
            allow_repeat = self.main_window.ui.GJ_checkBox.isChecked()
            mute_original = self.main_window.ui.GJ_checkBox_2.isChecked()
        else:
            # 高级设置不可见，使用默认值
            pd_min = 3
            pd_max = 5
            zsc_min = 15
            zsc_max = 31
            allow_repeat = False
            mute_original = False

        video_count = self.main_window.ui.HJ_spinBox_3.value()

        self.log_updated.emit(f"混剪参数：片段时长{pd_min}-{pd_max}秒，总时长{zsc_min}-{zsc_max}秒，生成{video_count}个视频")

        # 初始化生成文件列表
        generated_files = []

        # 生成指定数量的混剪视频
        for i in range(video_count):
            if self.should_stop:
                break

            self.log_updated.emit(f"\n开始生成第{i+1}个混剪视频...")

            # 生成混剪视频
            output_file = self.generate_single_mix_video(i+1, pd_min, pd_max, zsc_min, zsc_max, allow_repeat, mute_original)

            if output_file:
                generated_files.append(output_file)

            # 更新进度
            progress = int((i + 1) / video_count * 100)
            self.progress_updated.emit(progress)

        self.log_updated.emit("\n所有混剪视频生成完成！")

        # 清理中间文件
        self._cleanup_temp_files()

        # 执行后处理扩展功能（只有抽帧、背景音乐、重命名）
        if generated_files:
            self.log_updated.emit("\n开始执行后处理扩展功能...")

            # 按正确顺序执行后处理功能：背景音乐(倒数第三) -> 抽帧去重(倒数第二) -> 批量重命名(最后)
            if self.main_window.ui.YY_checkBox_3.isChecked():
                self.log_updated.emit("执行背景音乐功能...")
                generated_files = self.apply_hj_background_music(generated_files)

            if self.main_window.ui.CZ_checkBox_2.isChecked():
                self.log_updated.emit("执行抽帧去重功能...")
                generated_files = self.apply_hj_frame_removal(generated_files)

            if self.main_window.ui.CMM_checkBox_6.isChecked():
                self.log_updated.emit("执行批量重命名功能...")
                generated_files = self.apply_hj_batch_rename(generated_files)

            self.log_updated.emit("\n所有后处理功能执行完成！")

    def generate_single_mix_video(self, index, pd_min, pd_max, zsc_min, zsc_max, allow_repeat, mute_original):
        """生成单个混剪视频（在混剪过程中应用转场和落款）"""
        try:
            # 重置当前视频的转场选择，确保不同视频可以使用不同转场
            if hasattr(self, 'current_video_transition'):
                delattr(self, 'current_video_transition')

            output_name = f"混剪视频_{index:03d}.mp4"
            output_path = os.path.join(self.main_window.hj_output_dir, output_name)

            self.log_updated.emit(f"正在生成：{output_name}")

            # 检查是否有足够的素材
            if not self.main_window.hj_files:
                self.log_updated.emit("❌ 没有可用的混剪素材")
                return None

            # 简化的混剪实现：随机选择素材并合并
            import random

            # 计算需要的总时长
            target_duration = random.randint(zsc_min, zsc_max)
            self.log_updated.emit(f"目标时长：{target_duration}秒")

            # 随机选择素材文件
            selected_files = []
            current_duration = 0

            available_files = self.main_window.hj_files.copy()

            while current_duration < target_duration and available_files:
                # 随机选择一个文件
                file = random.choice(available_files)

                # 随机选择片段时长
                segment_duration = random.randint(pd_min, pd_max)

                selected_files.append((file, segment_duration))
                current_duration += segment_duration

                # 如果不允许重复使用，从可用列表中移除
                if not allow_repeat:
                    available_files.remove(file)

                # 如果允许重复但没有更多文件了，重新填充列表
                if allow_repeat and not available_files:
                    available_files = self.main_window.hj_files.copy()

            if not selected_files:
                self.log_updated.emit("❌ 无法选择足够的素材")
                return None

            self.log_updated.emit(f"选择了{len(selected_files)}个片段")

            # 检查是否需要添加落款
            ending_file = None
            if self.main_window.ui.HJ_checkBox_LK.isChecked():
                ending_file = self._get_ending_file()
                if ending_file:
                    self.log_updated.emit(f"将添加落款：{os.path.basename(ending_file)}")

            # 使用FFmpeg合并视频片段（包含转场和落款）
            success = self._merge_video_segments_with_effects(
                selected_files, output_path, mute_original, ending_file
            )

            if success:
                self.log_updated.emit(f"✅ 生成完成：{output_name}")
                return output_path
            else:
                self.log_updated.emit(f"❌ 生成失败：{output_name}")
                return None

        except Exception as e:
            self.log_updated.emit(f"❌ 生成混剪视频失败：{str(e)[:100]}")
            return None

    def _get_ending_file(self):
        """获取落款文件"""
        try:
            if self.main_window.ui.LK_radioButton_duo.isChecked():
                # 多个落款模式
                lk_folder = self.main_window.ui.LK_lineEdit_3.text().strip()
                if lk_folder and os.path.exists(lk_folder):
                    import glob
                    lk_files = []
                    for ext in ['*.mp4', '*.avi', '*.mkv', '*.mov']:
                        lk_files.extend(glob.glob(os.path.join(lk_folder, ext)))
                    if lk_files:
                        import random
                        return random.choice(lk_files)
            else:
                # 单个落款模式
                if self.main_window.lk_files:
                    return self.main_window.lk_files[0]
            return None
        except Exception:
            return None

    def _merge_video_segments_with_effects(self, selected_files, output_path, mute_original, ending_file=None):
        """合并视频片段（包含转场和落款效果）"""
        try:
            import subprocess
            import os

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 创建临时文件列表
            temp_files = []

            # 提取视频片段
            for i, (file, duration) in enumerate(selected_files):
                temp_name = f"temp_segment_{i}.mp4"
                temp_path = os.path.join(os.path.dirname(output_path), temp_name)

                # 提取指定时长的片段
                cmd = [
                    "ffmpeg",
                    "-i", file,
                    "-t", str(duration),
                    "-c", "copy",
                    "-y",
                    temp_path
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    creationflags=CREATE_NO_WINDOW
                )

                if result.returncode == 0:
                    temp_files.append(temp_path)
                else:
                    self.log_updated.emit(f"❌ 提取片段失败：{file}")
                    continue

            if not temp_files:
                return False

            # 如果有落款文件，需要特殊处理
            if ending_file:
                self.log_updated.emit(f"将添加落款：{os.path.basename(ending_file)}")

                # 检查落款文件是否需要预处理（确保格式一致）
                ending_temp_file = None
                try:
                    # 为落款文件创建临时处理版本，确保格式一致
                    ending_temp_name = f"temp_ending_{len(temp_files)}.mp4"
                    ending_temp_path = os.path.join(os.path.dirname(output_path), ending_temp_name)

                    # 使用copy模式快速处理落款文件
                    cmd = [
                        "ffmpeg",
                        "-i", ending_file,
                        "-c", "copy",
                        "-y",
                        ending_temp_path
                    ]

                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        encoding='utf-8',
                        creationflags=CREATE_NO_WINDOW
                    )

                    if result.returncode == 0:
                        ending_temp_file = ending_temp_path
                        temp_files.append(ending_temp_file)
                        self.log_updated.emit(f"✅ 落款文件预处理完成")
                    else:
                        # 如果预处理失败，直接使用原文件
                        temp_files.append(ending_file)
                        self.log_updated.emit(f"⚠️ 落款文件预处理失败，使用原文件")
                except Exception as e:
                    # 如果出错，直接使用原文件
                    temp_files.append(ending_file)
                    self.log_updated.emit(f"⚠️ 落款文件处理出错，使用原文件：{str(e)[:50]}")

            # 检查是否启用转场
            use_transition = self.main_window.ui.HJ_checkBox_ZC.isChecked() and len(temp_files) > 1

            if use_transition:
                # 使用转场效果合并
                success = self._merge_with_transitions(temp_files, output_path, mute_original)
            else:
                # 使用简单拼接
                success = self._merge_with_concat(temp_files, output_path, mute_original)

            # 清理临时文件（包括临时片段文件和临时落款文件）
            for temp_file in temp_files:
                # 删除所有临时文件，但不删除原始落款文件
                file_name = os.path.basename(temp_file)
                if (temp_file != ending_file and
                    (file_name.startswith("temp_segment_") or file_name.startswith("temp_ending_"))):
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                            self.log_updated.emit(f"🗑️ 清理临时文件：{file_name}")
                    except Exception as e:
                        self.log_updated.emit(f"⚠️ 清理临时文件失败：{str(e)[:50]}")

            return success

        except Exception as e:
            self.log_updated.emit(f"❌ 合并视频片段失败：{str(e)[:100]}")
            return False

    def _merge_with_transitions(self, temp_files, output_path, mute_original):
        """使用转场效果合并视频 - 简化版本，参考tab_QT的稳定实现"""
        try:
            import subprocess

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 清除上一个视频的转场选择，确保每个视频文件都能重新选择转场
            if hasattr(self, 'current_video_transition'):
                delattr(self, 'current_video_transition')

            # 获取转场设置
            transition_type = self.main_window.ui.HJ_comboBox_ZC.currentText()
            transition_duration = self.main_window.ui.ZC_doubleSpinBox_2.value()

            # 解析转场类型
            base_transition_effect = self.main_window._parse_transition_type(transition_type)

            self.log_updated.emit(f"转场类型: {transition_type}")
            self.log_updated.emit(f"转场基础效果: {base_transition_effect}")
            self.log_updated.emit(f"转场时长: {transition_duration}秒")

            # 检查是否使用多种转场（HJ_radioButton_y选中）
            use_multiple_transitions = self.main_window.ui.HJ_radioButton_y.isChecked()
            self.log_updated.emit(f"使用多种转场: {use_multiple_transitions}")

            # 为每个转场生成转场效果列表
            transition_effects = []
            import random

            # 定义转场效果池 - 按用户要求的完整列表
            all_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                             "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                             "distance", "diagtl", "diagtr", "diagbl", "diagbr", "wipeleft", "wiperight",
                             "wipeup", "wipedown", "slideleft", "slideright", "slideup", "slidedown",
                             "radial", "pixelize", "zoomin", "wipetl", "wipetr", "wipebl", "wipebr",
                             "hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]

            soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                              "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                              "distance", "diagtl", "diagtr", "diagbl", "diagbr"]

            hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright",
                              "slideup", "slidedown", "radial", "pixelize", "zoomin", "wipetl", "wipetr",
                              "wipebl", "wipebr", "hlslice", "hrslice", "vuslice", "vdslice",
                              "hlwind", "hrwind", "vuwind", "vdwind"]

            for i in range(1, len(temp_files)):
                if use_multiple_transitions:
                    # 每段都随机：每个转场都从选择的策略中随机选择
                    if transition_type == "【【全随机】】":
                        effect = random.choice(all_transitions)
                    elif transition_type == "【【柔 · 随机】】":
                        effect = random.choice(soft_transitions)
                    elif transition_type == "【【硬 · 随机】】":
                        effect = random.choice(hard_transitions)
                    else:
                        # 非随机转场：每个转场都从相似转场中选择
                        if base_transition_effect in ["fade"]:
                            similar_effects = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown"]
                        elif base_transition_effect in ["wipeleft", "wiperight", "wipeup", "wipedown"]:
                            similar_effects = ["wipeleft", "wiperight", "wipeup", "wipedown"]
                        elif base_transition_effect in ["slideleft", "slideright", "slideup", "slidedown"]:
                            similar_effects = ["slideleft", "slideright", "slideup", "slidedown"]
                        elif base_transition_effect in ["circleopen", "circleclose"]:
                            similar_effects = ["circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose"]
                        elif base_transition_effect in ["radial", "pixelize"]:
                            similar_effects = ["radial", "pixelize", "zoomin"]
                        else:
                            # 对于其他转场，使用原转场
                            similar_effects = [base_transition_effect]
                        effect = random.choice(similar_effects)
                else:
                    # 单个转场：单个视频内部用同一转场，不同视频之间可以用不同转场
                    # 注意：这里的逻辑是针对单个混剪视频内的多个片段
                    # 由于我们是在单个混剪视频的生成过程中，所以这里应该是所有片段用同一转场
                    if transition_type == "【【全随机】】":
                        if not hasattr(self, 'current_video_transition'):
                            # 为当前视频选择一个转场效果
                            self.current_video_transition = random.choice(all_transitions)
                            self.log_updated.emit(f"当前视频选择转场: {self.current_video_transition}")
                        effect = self.current_video_transition
                    elif transition_type == "【【柔 · 随机】】":
                        if not hasattr(self, 'current_video_transition'):
                            # 为当前视频选择一个转场效果
                            self.current_video_transition = random.choice(soft_transitions)
                            self.log_updated.emit(f"当前视频选择转场: {self.current_video_transition}")
                        effect = self.current_video_transition
                    elif transition_type == "【【硬 · 随机】】":
                        if not hasattr(self, 'current_video_transition'):
                            # 为当前视频选择一个转场效果
                            self.current_video_transition = random.choice(hard_transitions)
                            self.log_updated.emit(f"当前视频选择转场: {self.current_video_transition}")
                        effect = self.current_video_transition
                    else:
                        effect = base_transition_effect

                transition_effects.append(effect)
                self.log_updated.emit(f"转场 {i}: {effect}")

            # 简化实现：使用类似tab_QT的方式，逐步合并视频
            # 这样避免了复杂的多层滤镜导致的问题
            if len(temp_files) == 2:
                # 只有两个文件，直接使用tab_QT的方式
                return self._merge_two_videos_with_transition(temp_files[0], temp_files[1], output_path,
                                                            transition_effects[0], transition_duration, mute_original)
            else:
                # 多个文件，逐步合并
                return self._merge_multiple_videos_step_by_step(temp_files, output_path, transition_effects,
                                                              transition_duration, mute_original)

        except Exception as e:
            self.log_updated.emit(f"❌ 转场合并失败：{str(e)[:100]}")
            return False

    def _merge_two_videos_with_transition(self, video1, video2, output_path, transition_effect, transition_duration, mute_original):
        """合并两个视频，使用tab_QT的稳定方式 - 复用TransitionMixer"""
        try:
            # 导入TransitionMixer
            from transition_mixer import TransitionMixer

            # 查找FFmpeg路径
            from pathlib import Path
            ffmpeg_paths = [
                Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
                Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
                Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
                Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
            ]

            ffmpeg_path = None
            ffprobe_path = None
            for path in ffmpeg_paths:
                if path.exists():
                    ffmpeg_path = path
                    ffprobe_path = path.parent / "ffprobe.exe"
                    break

            if not ffmpeg_path or not ffprobe_path.exists():
                self.log_updated.emit("❌ 未找到FFmpeg或FFprobe")
                return False

            # 创建TransitionMixer实例
            mixer = TransitionMixer(ffmpeg_path, ffprobe_path, self.log_updated.emit)

            # 设置响度统一（tab_HJ默认启用响度统一）
            mixer.set_loudnorm_enabled(True)  # tab_HJ启用响度统一，避免声音越来越响

            # 使用TransitionMixer进行转场合并
            success = mixer.merge_two_videos_with_transition(
                Path(video1), Path(video2), Path(output_path),
                transition_effect, transition_duration, mute_original
            )

            return success

        except Exception as e:
            self.log_updated.emit(f"❌ 两视频转场失败：{str(e)[:100]}")
            return False

    def _merge_multiple_videos_step_by_step(self, temp_files, output_path, transition_effects, transition_duration, mute_original):
        """逐步合并多个视频，避免复杂滤镜导致的问题"""
        try:
            self.log_updated.emit(f"开始逐步合并{len(temp_files)}个视频")

            # 如果只有一个文件，直接复制
            if len(temp_files) == 1:
                import shutil
                shutil.copy2(temp_files[0], output_path)
                return True

            # 逐步合并：A+B=AB, AB+C=ABC, ABC+D=ABCD
            current_file = temp_files[0]

            for i in range(1, len(temp_files)):
                # 创建临时输出文件
                temp_output = os.path.join(os.path.dirname(output_path), f"step_{i}.mp4")

                # 获取当前转场效果
                transition_effect = transition_effects[i-1]

                self.log_updated.emit(f"步骤 {i}: 合并 {os.path.basename(current_file)} + {os.path.basename(temp_files[i])}")

                # 使用两视频合并方法
                success = self._merge_two_videos_with_transition(
                    current_file, temp_files[i], temp_output,
                    transition_effect, transition_duration, mute_original
                )

                if not success:
                    self.log_updated.emit(f"❌ 步骤 {i} 合并失败")
                    return False

                # 清理上一步的临时文件（但不删除原始文件）
                if i > 1 and current_file.startswith(os.path.join(os.path.dirname(output_path), "step_")):
                    try:
                        os.remove(current_file)
                        self.log_updated.emit(f"🗑️ 清理中间文件：{os.path.basename(current_file)}")
                    except:
                        pass

                current_file = temp_output

            # 将最终结果移动到目标位置
            import shutil
            shutil.move(current_file, output_path)

            self.log_updated.emit(f"✅ 逐步合并完成：{os.path.basename(output_path)}")
            return True

        except Exception as e:
            self.log_updated.emit(f"❌ 逐步合并失败：{str(e)[:100]}")
            return False

    def _merge_with_concat(self, temp_files, output_path, mute_original):
        """使用简单拼接合并视频"""
        try:
            import subprocess

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 创建concat文件
            concat_file = os.path.join(os.path.dirname(output_path), "concat_list.txt")
            with open(concat_file, 'w', encoding='utf-8') as f:
                for temp_file in temp_files:
                    f.write(f"file '{temp_file}'\n")

            # 合并所有片段
            cmd = [
                "ffmpeg",
                "-f", "concat",
                "-safe", "0",
                "-i", concat_file,
                "-c", "copy",
                "-y",
                output_path
            ]

            # 如果需要静音
            if mute_original:
                cmd = [
                    "ffmpeg",
                    "-f", "concat",
                    "-safe", "0",
                    "-i", concat_file,
                    "-c:v", "copy",
                    "-an",  # 移除音频
                    "-y",
                    output_path
                ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                creationflags=CREATE_NO_WINDOW
            )

            # 清理concat文件
            try:
                os.remove(concat_file)
            except:
                pass

            return result.returncode == 0

        except Exception as e:
            self.log_updated.emit(f"❌ 拼接合并失败：{str(e)[:100]}")
            return False



    def apply_hj_background_music(self, files):
        """应用背景音乐"""
        if not self.main_window.ui.YY_checkBox_3.isChecked():
            return files

        try:
            music_folder = self.main_window.ui.YY_lineEdit_2.text().strip()
            if not music_folder or not os.path.exists(music_folder):
                self.log_updated.emit("❌ 音乐文件夹路径无效")
                return files

            # 获取音乐文件
            import glob
            music_files = []
            for ext in ['*.mp3', '*.wav', '*.aac', '*.m4a']:
                music_files.extend(glob.glob(os.path.join(music_folder, ext)))

            if not music_files:
                self.log_updated.emit("❌ 音乐文件夹中没有找到音乐文件")
                return files

            processed_files = []
            for file in files:
                # 随机选择一个音乐文件
                import random
                music_file = random.choice(music_files)

                output_name = f"music_{os.path.basename(file)}"
                output_path = os.path.join(self.main_window.hj_output_dir, output_name)

                # 使用FFmpeg添加背景音乐
                success = self._add_background_music_to_video(file, music_file, output_path)

                if success:
                    processed_files.append(output_path)
                    self.log_updated.emit(f"✅ 添加背景音乐：{output_name}")
                else:
                    processed_files.append(file)
                    self.log_updated.emit(f"❌ 添加背景音乐失败：{os.path.basename(file)}")

            return processed_files

        except Exception as e:
            self.log_updated.emit(f"❌ 背景音乐处理失败：{str(e)[:100]}")
            return files

    def apply_hj_frame_removal(self, files):
        """应用抽帧去重"""
        if not self.main_window.ui.CZ_checkBox_2.isChecked():
            return files

        try:
            low_frames = self.main_window.ui.CZ_spinBox_low_2.value()
            max_frames = self.main_window.ui.CZ_spinBox_max_2.value()

            self.log_updated.emit(f"开始抽帧去重：删除{low_frames}-{max_frames}帧")

            processed_files = []
            for file in files:
                output_name = f"frame_removed_{os.path.basename(file)}"
                output_path = os.path.join(self.main_window.hj_output_dir, output_name)

                # 使用FFmpeg进行抽帧处理
                success = self._remove_frames_from_video(file, output_path, low_frames, max_frames)

                if success:
                    processed_files.append(output_path)
                    self.log_updated.emit(f"✅ 抽帧完成：{output_name}")
                else:
                    processed_files.append(file)
                    self.log_updated.emit(f"❌ 抽帧失败：{os.path.basename(file)}")

            return processed_files

        except Exception as e:
            self.log_updated.emit(f"❌ 抽帧处理失败：{str(e)[:100]}")
            return files

    def apply_hj_batch_rename(self, files):
        """应用批量重命名"""
        if not self.main_window.ui.CMM_checkBox_6.isChecked():
            return files

        try:
            start_value = self.main_window.ui.CMM_spinBox_3.value()
            prefix = self.main_window.ui.CMM_lineEdit_5.text()
            suffix = self.main_window.ui.CMM_lineEdit_6.text()
            fill_zeros = self.main_window.ui.spinBox_3.value()
            keep_extension = self.main_window.ui.CMM_checkBox_7.isChecked()

            self.log_updated.emit(f"开始批量重命名：{prefix}[数字]{suffix}")

            processed_files = []
            for i, file in enumerate(files, start=start_value):
                file_dir = os.path.dirname(file)
                original_ext = os.path.splitext(file)[1] if keep_extension else ".mp4"
                new_name = f"{prefix}{str(i).zfill(fill_zeros)}{suffix}{original_ext}"
                new_path = os.path.join(file_dir, new_name)

                try:
                    os.rename(file, new_path)
                    processed_files.append(new_path)
                    self.log_updated.emit(f"✅ 重命名：{os.path.basename(file)} → {new_name}")
                except Exception as e:
                    processed_files.append(file)
                    self.log_updated.emit(f"❌ 重命名失败：{str(e)[:50]}")

            return processed_files

        except Exception as e:
            self.log_updated.emit(f"❌ 批量重命名失败：{str(e)[:100]}")
            return files



    def _add_background_music_to_video(self, video_file, music_file, output_path):
        """为视频添加背景音乐"""
        try:
            import subprocess

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 检查是否保留原音频（高级设置不可见或GJ_checkBox_2未勾选时保留）
            keep_original_audio = True
            if self.main_window.ui.HJ_groupBox_GJ.isVisible():
                keep_original_audio = not self.main_window.ui.GJ_checkBox_2.isChecked()

            if keep_original_audio:
                # 保留原音频，与背景音乐混合，先响度统一再音量调节
                cmd = [
                    "ffmpeg",
                    "-i", video_file,
                    "-i", music_file,
                    "-filter_complex", "[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0,volume=0.8[a0];[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0,volume=0.6[a1];[a0][a1]amix=inputs=2:duration=first:normalize=0[aout]",
                    "-map", "0:v:0",
                    "-map", "[aout]",
                    "-c:v", "copy",
                    "-c:a", "aac",
                    "-b:a", "256k",  # 提高音频码率，减少失真
                    "-ar", "48000",  # 使用48kHz采样率，提高音质
                    "-shortest",
                    "-y",
                    output_path
                ]
                self.log_updated.emit(f"🎵 保留原音频并添加背景音乐（含响度统一）")
            else:
                # 只使用背景音乐，替换原音频，先响度统一
                cmd = [
                    "ffmpeg",
                    "-i", video_file,
                    "-i", music_file,
                    "-filter_complex", "[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[aout]",
                    "-map", "0:v:0",
                    "-map", "[aout]",
                    "-c:v", "copy",
                    "-c:a", "aac",
                    "-shortest",
                    "-y",
                    output_path
                ]
                self.log_updated.emit(f"🎵 替换原音频为背景音乐（含响度统一）")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                creationflags=CREATE_NO_WINDOW
            )

            # 清理原文件
            if result.returncode == 0:
                try:
                    os.remove(video_file)
                    self.log_updated.emit(f"🗑️ 清理原文件：{os.path.basename(video_file)}")
                except:
                    pass

            return result.returncode == 0

        except Exception:
            return False

    def _remove_frames_from_video(self, video_file, output_path, low_frames, max_frames):
        """从视频中移除指定帧数 - 复用tab_QT的实现"""
        try:
            import subprocess
            import random

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 获取视频元数据
            from utils import get_video_metadata
            meta = get_video_metadata(video_file)
            if not meta:
                self.log_updated.emit(f"❌ 无法获取视频元数据：{os.path.basename(video_file)}")
                return False

            # 检查帧数信息
            video_stream = meta.get('streams', [{}])[0]
            if 'nb_frames' not in video_stream:
                self.log_updated.emit(f"❌ 无法获取帧数信息：{os.path.basename(video_file)}")
                return False

            total_frames = int(video_stream['nb_frames'])
            delete_frames = random.randint(low_frames, max_frames)

            if total_frames < delete_frames:
                self.log_updated.emit(f"❌ 帧数不足：{os.path.basename(video_file)} ({total_frames} < {delete_frames})")
                return False

            # 随机选择要删除的帧
            frame_ids = sorted(random.sample(range(total_frames), delete_frames))
            frame_duration = float(meta["format"]["duration"]) / total_frames
            time_expr = [
                f"between(t,{f*frame_duration:.6f},{(f+1)*frame_duration:.6f})"
                for f in frame_ids
            ]

            # 构建基础FFmpeg命令
            base_cmd = [
                "ffmpeg",
                "-i", video_file,
                "-hide_banner",
                "-vf", f"select='not({'+'.join(time_expr)})',setpts=N/FRAME_RATE/TB",
                "-af", f"aselect='not({'+'.join(time_expr)})',asetpts=N/SR/TB",
                "-c:v", "placeholder",  # 将被GPU函数替换
                "-b:v", VIDEO_BITRATE,
                "-maxrate", "18000k",
                "-bufsize", "36000k",
                "-c:a", "aac",
                "-b:a", "320k",
                "-y",
                output_path
            ]

            # 使用统一的GPU加速函数
            cmd, using_gpu = build_gpu_ffmpeg_cmd(base_cmd)
            if using_gpu:
                self.log_updated.emit("🚀 使用GPU硬件加速处理")
            else:
                self.log_updated.emit("🔄 使用CPU处理")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                # 清理原文件
                try:
                    os.remove(video_file)
                except:
                    pass

                # 输出处理结果
                output_meta = get_video_metadata(output_path)
                if output_meta and 'nb_frames' in output_meta.get('streams', [{}])[0]:
                    output_frames = int(output_meta["streams"][0]["nb_frames"])
                    self.log_updated.emit(f"✅ 抽帧完成：{os.path.basename(video_file)} (原帧:{total_frames} → 新帧:{output_frames})")
                else:
                    self.log_updated.emit(f"✅ 抽帧完成：{os.path.basename(video_file)}")

            return result.returncode == 0

        except Exception as e:
            self.log_updated.emit(f"❌ 抽帧失败：{str(e)[:100]}")
            return False

    def _cleanup_temp_files(self):
        """清理中间文件"""
        try:
            output_dir = self.main_window.hj_output_dir
            if not output_dir or not os.path.exists(output_dir):
                return

            # 获取所有文件
            all_files = [
                os.path.join(output_dir, f)
                for f in os.listdir(output_dir)
                if os.path.isfile(os.path.join(output_dir, f))
            ]

            # 定义需要保留的文件模式
            keep_patterns = [
                "混剪视频_",  # 主要混剪文件
                "music_",    # 添加音乐后的文件
                "frame_removed_",  # 抽帧后的文件
            ]

            # 定义中间文件模式
            temp_patterns = [
                "temp_segment_",  # 临时片段文件
                "concat_list.txt",  # concat列表文件
            ]

            files_to_delete = []
            for file in all_files:
                file_name = os.path.basename(file)

                # 检查是否是中间文件
                is_temp = any(pattern in file_name for pattern in temp_patterns)

                # 检查是否是需要保留的文件
                is_keep = any(pattern in file_name for pattern in keep_patterns)

                if is_temp or (not is_keep and file_name.startswith("temp_")):
                    files_to_delete.append(file)

            # 删除中间文件
            deleted_count = 0
            for file in files_to_delete:
                try:
                    os.remove(file)
                    deleted_count += 1
                except Exception:
                    pass

            if deleted_count > 0:
                self.log_updated.emit(f"🗑️ 清理了{deleted_count}个中间文件")

        except Exception as e:
            self.log_updated.emit(f"❌ 清理中间文件失败：{str(e)[:100]}")




# 自定义拖放列表视图类（支持多列表区分）
class DragDropListView(QListView):
    def __init__(self, main_window, list_type, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.list_type = list_type  # 区分前贴/后贴列表类型
        self.setAcceptDrops(True)
        self.setDragDropMode(QListView.DropOnly)
        self.setDefaultDropAction(Qt.CopyAction)
        self.setStyleSheet(
            """
            QListView {
                border: 2px solid #ccc;
                border-radius: 4px;
                padding: 5px;
            }
            QListView::drop-indicator {
                background-color: #a6c9e2;
                border-top: 2px solid #3a6ea5;
            }
        """
        )
        # 初始化 modified_files 属性
        self.modified_files = {}  # 用于存储修改后的文件

    def dragEnterEvent(self, event: QDragEnterEvent):
        #print("【调试】dragEnterEvent 触发")  # 添加调试打印
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                if url.isLocalFile() and self._is_video_file(url.toLocalFile()):
                    event.acceptProposedAction()
                    return
        event.ignore()

    def dragMoveEvent(self, event: QDragEnterEvent):
        print("【调试】dragMoveEvent 触发")  # 添加调试打印
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                if url.isLocalFile() and self._is_video_file(url.toLocalFile()):
                    event.acceptProposedAction()
                    return
        event.ignore()

    def dropEvent(self, event: QDropEvent):
        print("【调试】dropEvent 触发")  # 添加调试打印
        if event.mimeData().hasUrls():
            files = [url.toLocalFile() for url in event.mimeData().urls()]
            self.main_window.add_files_from_drag(files, self.list_type)
            event.acceptProposedAction()

    def _is_video_file(self, file_path):
        video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv"]
        suffix = Path(file_path).suffix.lower()
        print(f"【调试】文件 {file_path} 的后缀是：{suffix}")  # 添加调试打印
        return suffix in video_extensions


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)

        # 初始化设置
        self.settings = QSettings("YourCompany", "YourAppName")

        # 设置默认状态
        self.ui.CMM_checkBox_3.setChecked(True)  # 保留扩展名默认勾选
        self.ui.CMM_checkBox_4.setChecked(False)  # 批量重命名默认不勾选

        # 读取用户设置
        self._load_settings()

        # 确保 CMM_checkBox_4 始终为不勾选状态（不受设置影响）
        self.ui.CMM_checkBox_4.setChecked(False)

        # 初始化混剪器
        self.mixer = VideoMixer()
        self.mixer.set_log_callback(self.update_log)

        # 为tab_QT设置响度统一（默认启用，确保前贴和后贴素材响度统一）
        self.mixer.set_loudnorm_enabled(True)

        # 数据存储
        self.main_files = []  # 前贴文件
        self.variant_files = []  # 后贴文件
        self.output_dir = ""  # 输出目录
        self.selected_strategy = None
        self.is_running = False

        # 初始化抽帧tab的相关控件
        self.frame_files = []  # 存储要处理的文件
        self.frame_model = QStringListModel()  # 列表模型
        self.frame_processor = None  # 抽帧处理器实例

        # 初始化混剪tab的相关控件
        self.hj_files = []  # 存储混剪素材文件
        self.hj_model = QStringListModel()  # 混剪列表模型
        self.hj_output_dir = ""  # 混剪输出目录
        self.hj_is_running = False  # 混剪运行状态
        self.lk_files = []  # 存储落款文件
        self.lk_model = QStringListModel()  # 落款列表模型

        # 初始化随机混剪tab的相关控件
        self.sjhj_files = []  # 存储前贴文件
        self.sjhj_model = QStringListModel()  # 前贴列表模型
        self.sjhj_output_dir = ""  # 输出目录
        self.sjhj_is_running = False  # 运行状态
        self.sjhj_variant_files = []  # 后贴文件列表（从目录获取）

        # 替换前贴列表视图
        #print("【调试】正在替换前贴列表视图...")  # 添加调试打印
        self.qt_list_view = DragDropListView(self, "main", self.ui.SPZD_groupBox)
        self.qt_list_view.setObjectName("QT_listView")
        self.qt_list_view.setGeometry(self.ui.QT_listView.geometry())
        #print(f"【调试】前贴列表视图原始对象: {self.ui.QT_listView}")  # 添加调试打印
        self.ui.QT_listView.deleteLater()
        self.ui.QT_listView = self.qt_list_view
        #print(f"【调试】前贴列表视图替换后对象: {self.ui.QT_listView}")  # 添加调试打印

        # 替换随机混剪tab的前贴列表视图
        self.sjhj_list_view = DragDropListView(self, "sjhj", self.ui.SJHJ_groupBox_3)
        self.sjhj_list_view.setObjectName("SJHJ_listView_5")
        self.sjhj_list_view.setGeometry(self.ui.SJHJ_listView_5.geometry())
        self.ui.SJHJ_listView_5.deleteLater()
        self.ui.SJHJ_listView_5 = self.sjhj_list_view

        # 替换后贴列表视图
        #print("【调试】正在替换后贴列表视图...")  # 添加调试打印
        self.ht_list_view = DragDropListView(self, "variant", self.ui.SPZD_groupBox)
        self.ht_list_view.setObjectName("HT_listView_2")
        self.ht_list_view.setGeometry(self.ui.HT_listView_2.geometry())
        #print(f"【调试】后贴列表视图原始对象: {self.ui.HT_listView_2}")  # 添加调试打印
        self.ui.HT_listView_2.deleteLater()
        self.ui.HT_listView_2 = self.ht_list_view
        # print(
        #     f"【调试】后贴列表视图替换后对象: {self.ui.HT_listView_2}"
        # )  # 添加调试打印

        # 初始化列表模型
        self.qt_model = QStringListModel()
        self.ui.QT_listView.setModel(self.qt_model)
        self.ht_model = QStringListModel()
        self.ui.HT_listView_2.setModel(self.ht_model)

        # 初始化随机混剪tab的列表模型
        self.ui.SJHJ_listView_5.setModel(self.sjhj_model)

        # 按钮事件连接
        self.ui.QT_in.clicked.connect(self.add_main_files)
        self.ui.QT_out.clicked.connect(self.remove_selected_main_files)
        self.ui.QT_clean.clicked.connect(self.clear_main_files)
        self.ui.HT_in.clicked.connect(self.add_variant_files)
        self.ui.HT_out.clicked.connect(self.remove_selected_variant_files)
        self.ui.HT_clean.clicked.connect(self.clear_variant_files)
        self.ui.SC_where.clicked.connect(self.select_output_directory)
        self.ui.CSHJC.clicked.connect(self.run_initial_check)
        self.ui.KS_pushButton.clicked.connect(self.start_mix)
        self.ui.TZ_pushButton.clicked.connect(self.stop_mix)

        # 策略布局初始化
        self.strategy_layout = QVBoxLayout()
        self.strategy_buttons = []
        self.ui.CL_widget.setLayout(self.strategy_layout)
        self.ui.KS_pushButton.setEnabled(False)
        self.ui.TZ_pushButton.setEnabled(False)
        self.ui.progressBar.setValue(0)

        # 连接扩展功能控件的信号
        self.ui.CZ_spinBox_low.valueChanged.connect(self._save_settings)
        self.ui.CZ_spinBox_max.valueChanged.connect(self._save_settings)
        self.ui.YY_pushButton.clicked.connect(self._select_music_folder)
        self.ui.YY_spinBox_yyyl.valueChanged.connect(self._save_settings)
        self.ui.YY_spinBox_yspyl.valueChanged.connect(self._save_settings)
        self.ui.CMM_spinBox.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_2.textChanged.connect(self._save_settings)
        self.ui.spinBox.valueChanged.connect(self._save_settings)
        self.ui.CMM_checkBox_3.setChecked(True)

        # 连接转场功能控件的信号
        self.ui.ZC_checkBox.toggled.connect(self._save_settings)
        self.ui.ZC_comboBox.currentTextChanged.connect(self._save_settings)
        self.ui.ZC_doubleSpinBox.valueChanged.connect(self._save_settings)

        # 替换抽帧列表视图
        self.frame_list_view = DragDropListView(self, "frame", self.ui.DDCZ_groupBox)
        self.frame_list_view.setObjectName("DDCZ_listView_3")
        self.frame_list_view.setGeometry(self.ui.DDCZ_listView_3.geometry())
        self.ui.DDCZ_listView_3.deleteLater()
        self.ui.DDCZ_listView_3 = self.frame_list_view
        self.ui.DDCZ_listView_3.setModel(self.frame_model)

        # 连接抽帧tab的信号
        self.ui.DDCZ_in_3.clicked.connect(self.add_frame_files)
        self.ui.DDCZ_out_3.clicked.connect(self.remove_selected_frame_files)
        self.ui.DDCZ_clean_3.clicked.connect(self.clear_frame_files)
        self.ui.DDCZ_where_3.clicked.connect(self.select_frame_output_directory)
        self.ui.DDCZ_pushButton_2.clicked.connect(self.start_frame_removal)
        self.ui.DDCZ_pushButton_3.clicked.connect(self.stop_frame_removal)

        # 连接单选按钮信号
        self.ui.DDCZ_radioButton_TH.toggled.connect(self.on_frame_radio_toggled)
        self.ui.DDCZ_radioButton_ZD.toggled.connect(self.on_frame_radio_toggled)

        # 设置默认选择
        self.ui.DDCZ_radioButton_ZD.setChecked(True)

        # 加载抽帧设置
        self.ui.DDCZ_spinBox_low_2.setValue(
            self.settings.value("DDCZ_spinBox_low_2", 15, type=int)
        )
        self.ui.DDCZ_spinBox_max_2.setValue(
            self.settings.value("DDCZ_spinBox_max_2", 50, type=int)
        )

        # 连接抽帧设置保存
        self.ui.DDCZ_spinBox_low_2.valueChanged.connect(self._save_settings)
        self.ui.DDCZ_spinBox_max_2.valueChanged.connect(self._save_settings)

        # 连接抽帧重命名设置保存
        self.ui.CMM_spinBox_2.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_3.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_4.textChanged.connect(self._save_settings)
        self.ui.spinBox_2.valueChanged.connect(self._save_settings)
        self.ui.CMM_checkBox_5.toggled.connect(self._save_settings)
        # CMM_checkBox_4 不保存设置，每次都默认为不勾选状态

        # 替换混剪tab的列表视图
        self.hj_list_view = DragDropListView(self, "hj", self.ui.HJSPZD_groupBox_2)
        self.hj_list_view.setObjectName("HJ_listView_4")
        self.hj_list_view.setGeometry(self.ui.HJ_listView_4.geometry())
        self.ui.HJ_listView_4.deleteLater()
        self.ui.HJ_listView_4 = self.hj_list_view
        self.ui.HJ_listView_4.setModel(self.hj_model)

        # 替换落款列表视图
        self.lk_list_view = DragDropListView(self, "lk", self.ui.HJ_groupBox_LK)
        self.lk_list_view.setObjectName("LK_listView")
        self.lk_list_view.setGeometry(self.ui.LK_listView.geometry())
        self.ui.LK_listView.deleteLater()
        self.ui.LK_listView = self.lk_list_view
        self.ui.LK_listView.setModel(self.lk_model)

        # 连接混剪tab的信号
        self.ui.HJ_in_4.clicked.connect(self.add_hj_files)
        self.ui.HJ_out_4.clicked.connect(self.remove_selected_hj_files)
        self.ui.HJ_clean_4.clicked.connect(self.clear_hj_files)
        self.ui.HJSC_where_4.clicked.connect(self.select_hj_output_directory)
        self.ui.CSHJC_4.clicked.connect(self.run_hj_initial_check)
        self.ui.HJKS_pushButton_2.clicked.connect(self.start_hj_mix)
        self.ui.HJTZ_pushButton_2.clicked.connect(self.stop_hj_mix)
        self.ui.HJ_pushButton_GJ.clicked.connect(self.toggle_hj_advanced_settings)

        # 连接落款相关信号
        self.ui.LK_in_5.clicked.connect(self.add_lk_files)
        self.ui.LK_clean_5.clicked.connect(self.clear_lk_files)
        self.ui.LK_pushButton_3.clicked.connect(self.select_lk_folder)
        self.ui.LK_radioButton_duo.toggled.connect(self.on_lk_radio_toggled)
        self.ui.LK_radioButton_dan.toggled.connect(self.on_lk_radio_toggled)

        # 连接tab_HJ的背景音乐按钮
        self.ui.YY_pushButton_2.clicked.connect(self.select_hj_music_folder)

        # 连接转场相关信号
        self.ui.HJ_checkBox_ZC.toggled.connect(self.on_hj_transition_toggled)
        self.ui.HJ_comboBox_ZC.currentTextChanged.connect(self.on_hj_transition_changed)

        # 连接随机混剪tab的信号
        self.ui.SJHJ_in_5.clicked.connect(self.add_sjhj_files)
        self.ui.SJHJ_out_5.clicked.connect(self.remove_selected_sjhj_files)
        self.ui.SJHJ_clean_5.clicked.connect(self.clear_sjhj_files)
        self.ui.SJHJSC_where_5.clicked.connect(self.select_sjhj_output_directory)
        self.ui.SJCSHJC_5.clicked.connect(self.run_sjhj_initial_check)
        self.ui.SJHJKS_pushButton_3.clicked.connect(self.start_sjhj_mix)
        self.ui.SJHJTZ_pushButton_3.clicked.connect(self.stop_sjhj_mix)

        # 连接后贴素材库相关信号
        self.ui.SJHJ_radioButton_M.toggled.connect(self.on_sjhj_material_radio_toggled)
        self.ui.SJHJ_radioButton_Z.toggled.connect(self.on_sjhj_material_radio_toggled)
        self.ui.SJHJ_radioButton_FBL1080.toggled.connect(self.on_sjhj_resolution_changed)
        self.ui.SJHJ_radioButton_FBL720.toggled.connect(self.on_sjhj_resolution_changed)
        self.ui.SJHJ_radioButton_ZL30.toggled.connect(self.on_sjhj_framerate_changed)
        self.ui.SJHJ_radioButton_ZL60.toggled.connect(self.on_sjhj_framerate_changed)
        self.ui.SJHJSC_open.clicked.connect(self.open_sjhj_material_folder)
        self.ui.SJHJSC_where_6.clicked.connect(self.select_sjhj_custom_folder)

        # 设置随机混剪tab的单选按钮组
        # 组A：素材库选择组（默认后台库 vs 自定义库）
        self.sjhj_material_group = QButtonGroup(self)
        self.sjhj_material_group.addButton(self.ui.SJHJ_radioButton_M)
        self.sjhj_material_group.addButton(self.ui.SJHJ_radioButton_Z)

        # 组B：分辨率选择组（1080P vs 720P）
        self.sjhj_resolution_group = QButtonGroup(self)
        self.sjhj_resolution_group.addButton(self.ui.SJHJ_radioButton_FBL1080)
        self.sjhj_resolution_group.addButton(self.ui.SJHJ_radioButton_FBL720)

        # 组C：帧率选择组（30帧 vs 60帧）
        self.sjhj_framerate_group = QButtonGroup(self)
        self.sjhj_framerate_group.addButton(self.ui.SJHJ_radioButton_ZL30)
        self.sjhj_framerate_group.addButton(self.ui.SJHJ_radioButton_ZL60)

        # 设置默认状态
        self.ui.SJHJ_radioButton_M.setChecked(True)  # 默认选择默认后台库
        self.ui.SJHJ_radioButton_FBL1080.setChecked(True)  # 默认选择1080P
        self.ui.SJHJ_radioButton_ZL30.setChecked(True)  # 默认选择30帧
        self.ui.SJHJ_spinBox_4.setValue(1)  # 默认每个前贴用1次

        # 初始化控件状态
        self.on_sjhj_material_radio_toggled()

        # 连接随机混剪tab设定区控件的信号以保存设置
        self.ui.SJHJSC_lineEdit_6.textChanged.connect(self._save_settings)
        self.ui.SJHJ_spinBox_4.valueChanged.connect(self._save_settings)
        self.ui.SJHJZC_checkBox_2.toggled.connect(self._save_settings)
        self.ui.SJHJZC_comboBox_2.currentTextChanged.connect(self._save_settings)
        self.ui.SJHJZC_doubleSpinBox_3.valueChanged.connect(self._save_settings)
        self.ui.CMM_checkBox_9.toggled.connect(self._save_settings)
        self.ui.CMM_spinBox_4.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_8.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_7.textChanged.connect(self._save_settings)
        self.ui.CMM_checkBox_8.toggled.connect(self._save_settings)
        self.ui.spinBox_4.valueChanged.connect(self._save_settings)

        # 连接tab_HJ相关设置保存信号
        self.ui.CZ_spinBox_low_2.valueChanged.connect(self._save_settings)
        self.ui.CZ_spinBox_max_2.valueChanged.connect(self._save_settings)
        self.ui.YY_lineEdit_2.textChanged.connect(self._save_settings)
        self.ui.YY_spinBox_yspyl_2.valueChanged.connect(self._save_settings)
        self.ui.YY_spinBox_yyyl_2.valueChanged.connect(self._save_settings)
        self.ui.CMM_spinBox_3.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_5.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_6.textChanged.connect(self._save_settings)
        self.ui.spinBox_3.valueChanged.connect(self._save_settings)
        self.ui.LK_lineEdit_3.textChanged.connect(self._save_settings)

        # 设置默认状态
        self.ui.HJ_groupBox_GJ.setVisible(False)  # 默认隐藏高级设置
        self.ui.LK_radioButton_dan.setChecked(True)  # 默认选择单个落款
        self.ui.HJ_radioButton_n.setChecked(True)  # 默认选择只用一个转场

        # 设置混剪tab的默认值
        self.ui.GJ_spinBox_PDmin.setValue(3)  # 片段最短时长默认3秒
        self.ui.GJ_spinBox_PDmax.setValue(5)  # 片段最长时长默认5秒
        self.ui.GJ_spinBox_ZSCmin.setValue(15)  # 总时长最短默认15秒
        self.ui.GJ_spinBox_ZSCmax.setValue(31)  # 总时长最长默认31秒
        self.ui.GJ_checkBox.setChecked(False)  # 默认不重复使用素材
        self.ui.GJ_checkBox_2.setChecked(False)  # 默认不静音原素材
        self.ui.HJ_spinBox_3.setValue(1)  # 默认生成1个视频

        self.on_lk_radio_toggled()  # 初始化落款控件状态
        self.on_hj_transition_toggled()  # 初始化转场控件状态

    def change_extensions(self, files, new_ext):
        """修改文件扩展名"""
        renamed_files = []
        for file in files:
            file_dir = os.path.dirname(file)
            file_name = os.path.basename(file)
            base_name, _ = os.path.splitext(file_name)
            new_name = f"{base_name}{new_ext}"
            new_path = os.path.join(file_dir, new_name)
            try:
                os.rename(file, new_path)
                self.update_log(f"✅ 修改扩展名：{file} → {new_path}")
                renamed_files.append(new_path)
            except Exception as e:
                self.update_log(f"❌ 修改扩展名失败：{str(e)[:50]}")
                renamed_files.append(file)  # 失败时保留原文件
        return renamed_files

    def restore_extensions(self):
        """恢复所有文件的原始扩展名"""
        if not hasattr(self, 'modified_files') or not self.modified_files:
            return

        # 创建原始文件路径到临时文件路径的映射
        original_to_temp = {
            Path(f).with_suffix('.MP4'): Path(f)
            for f in self.modified_files
        }

        # 恢复扩展名
        for original_path, temp_path in original_to_temp.items():
            if temp_path.exists():
                try:
                    temp_path.rename(original_path)
                    self.update_log(f"✅ 恢复扩展名：{temp_path} → {original_path}")
                except Exception as e:
                    self.update_log(f"❌ 恢复扩展名失败：{str(e)[:50]}")
            else:
                self.update_log(f"⚠️ 文件已不存在：{temp_path}")

        # 清理临时映射
        delattr(self, 'modified_files')

    def _load_settings(self):
        # 抽帧去重设置
        self.ui.CZ_spinBox_low.setValue(
            self.settings.value("CZ_spinBox_low", 15, type=int)
        )
        self.ui.CZ_spinBox_max.setValue(
            self.settings.value("CZ_spinBox_max", 50, type=int)
        )

        # 转场功能设置 - 默认不启用
        self.ui.ZC_checkBox.setChecked(False)
        self.ui.ZC_comboBox.setCurrentText(
            self.settings.value("ZC_comboBox", "【【全随机】】")
        )
        self.ui.ZC_doubleSpinBox.setValue(
            self.settings.value("ZC_doubleSpinBox", 1.0, type=float)
        )

        # 整体添加背景音乐设置
        music_folder = self.settings.value("YY_lineEdit", "")
        self.ui.YY_lineEdit.setText(music_folder)
        self.ui.YY_spinBox_yyyl.setValue(
            self.settings.value("YY_spinBox_yyyl", 0, type=int)
        )
        self.ui.YY_spinBox_yspyl.setValue(
            self.settings.value("YY_spinBox_yspyl", 0, type=int)
        )

        # 批量重命名设置
        self.ui.CMM_spinBox.setValue(self.settings.value("CMM_spinBox", 1, type=int))
        self.ui.CMM_lineEdit.setText(self.settings.value("CMM_lineEdit", ""))
        self.ui.CMM_lineEdit_2.setText(self.settings.value("CMM_lineEdit_2", ""))
        self.ui.spinBox.setValue(self.settings.value("spinBox", 1, type=int))

        # 加载抽帧重命名设置
        self.ui.CMM_spinBox_2.setValue(
            self.settings.value("CMM_spinBox_2", 1, type=int)
        )
        self.ui.CMM_lineEdit_3.setText(
            self.settings.value("CMM_lineEdit_3", "")
        )
        self.ui.CMM_lineEdit_4.setText(
            self.settings.value("CMM_lineEdit_4", "")
        )
        self.ui.spinBox_2.setValue(
            self.settings.value("spinBox_2", 1, type=int)
        )
        self.ui.CMM_checkBox_5.setChecked(
            self.settings.value("CMM_checkBox_5", True, type=bool)
        )
        # CMM_checkBox_4 不记录用户上次选择，始终默认为不勾选状态
        self.ui.CMM_checkBox_4.setChecked(False)

        # 加载tab_HJ相关设置
        self.ui.CZ_spinBox_low_2.setValue(
            self.settings.value("CZ_spinBox_low_2", 15, type=int)
        )
        self.ui.CZ_spinBox_max_2.setValue(
            self.settings.value("CZ_spinBox_max_2", 50, type=int)
        )
        self.ui.YY_lineEdit_2.setText(
            self.settings.value("YY_lineEdit_2", "")
        )
        self.ui.YY_spinBox_yspyl_2.setValue(
            self.settings.value("YY_spinBox_yspyl_2", 5, type=int)
        )
        self.ui.YY_spinBox_yyyl_2.setValue(
            self.settings.value("YY_spinBox_yyyl_2", 3, type=int)
        )
        self.ui.CMM_spinBox_3.setValue(
            self.settings.value("CMM_spinBox_3", 1, type=int)
        )
        self.ui.CMM_lineEdit_5.setText(
            self.settings.value("CMM_lineEdit_5", "")
        )
        self.ui.CMM_lineEdit_6.setText(
            self.settings.value("CMM_lineEdit_6", "")
        )
        self.ui.spinBox_3.setValue(
            self.settings.value("spinBox_3", 3, type=int)
        )

        # 加载LK_lineEdit_3设置
        self.ui.LK_lineEdit_3.setText(
            self.settings.value("LK_lineEdit_3", "")
        )

        # 加载YY_lineEdit_2设置
        self.ui.YY_lineEdit_2.setText(
            self.settings.value("YY_lineEdit_2", "")
        )

        # 加载随机混剪tab设置
        self.ui.SJHJSC_lineEdit_6.setText(
            self.settings.value("SJHJSC_lineEdit_6", "")
        )
        self.ui.SJHJ_spinBox_4.setValue(
            self.settings.value("SJHJ_spinBox_4", 1, type=int)
        )
        self.ui.SJHJZC_checkBox_2.setChecked(
            self.settings.value("SJHJZC_checkBox_2", False, type=bool)
        )
        self.ui.SJHJZC_comboBox_2.setCurrentText(
            self.settings.value("SJHJZC_comboBox_2", "【【全随机】】")
        )
        self.ui.SJHJZC_doubleSpinBox_3.setValue(
            self.settings.value("SJHJZC_doubleSpinBox_3", 1.0, type=float)
        )
        self.ui.CMM_checkBox_9.setChecked(
            self.settings.value("CMM_checkBox_9", False, type=bool)
        )
        self.ui.CMM_spinBox_4.setValue(
            self.settings.value("CMM_spinBox_4", 1, type=int)
        )
        self.ui.CMM_lineEdit_8.setText(
            self.settings.value("CMM_lineEdit_8", "")
        )
        self.ui.CMM_lineEdit_7.setText(
            self.settings.value("CMM_lineEdit_7", "")
        )
        self.ui.CMM_checkBox_8.setChecked(
            self.settings.value("CMM_checkBox_8", True, type=bool)
        )
        self.ui.spinBox_4.setValue(
            self.settings.value("spinBox_4", 3, type=int)
        )

    def _save_settings(self):
        # 抽帧去重设置
        self.settings.setValue("CZ_spinBox_low", self.ui.CZ_spinBox_low.value())
        self.settings.setValue("CZ_spinBox_max", self.ui.CZ_spinBox_max.value())

        # 转场功能设置
        self.settings.setValue("ZC_checkBox", self.ui.ZC_checkBox.isChecked())
        self.settings.setValue("ZC_comboBox", self.ui.ZC_comboBox.currentText())
        self.settings.setValue("ZC_doubleSpinBox", self.ui.ZC_doubleSpinBox.value())

        # 整体添加背景音乐设置
        self.settings.setValue("YY_lineEdit", self.ui.YY_lineEdit.text())
        self.settings.setValue("YY_spinBox_yyyl", self.ui.YY_spinBox_yyyl.value())
        self.settings.setValue("YY_spinBox_yspyl", self.ui.YY_spinBox_yspyl.value())

        # 批量重命名设置
        self.settings.setValue("CMM_spinBox", self.ui.CMM_spinBox.value())
        self.settings.setValue("CMM_lineEdit", self.ui.CMM_lineEdit.text())
        self.settings.setValue("CMM_lineEdit_2", self.ui.CMM_lineEdit_2.text())
        self.settings.setValue("spinBox", self.ui.spinBox.value())

        # 保存抽帧设置
        self.settings.setValue("DDCZ_spinBox_low_2", self.ui.DDCZ_spinBox_low_2.value())
        self.settings.setValue("DDCZ_spinBox_max_2", self.ui.DDCZ_spinBox_max_2.value())

        # 保存抽帧重命名设置
        self.settings.setValue("CMM_spinBox_2", self.ui.CMM_spinBox_2.value())
        self.settings.setValue("CMM_lineEdit_3", self.ui.CMM_lineEdit_3.text())
        self.settings.setValue("CMM_lineEdit_4", self.ui.CMM_lineEdit_4.text())
        self.settings.setValue("spinBox_2", self.ui.spinBox_2.value())
        self.settings.setValue("CMM_checkBox_5", self.ui.CMM_checkBox_5.isChecked())
        # CMM_checkBox_4 不保存设置

        # 保存tab_HJ相关设置
        self.settings.setValue("CZ_spinBox_low_2", self.ui.CZ_spinBox_low_2.value())
        self.settings.setValue("CZ_spinBox_max_2", self.ui.CZ_spinBox_max_2.value())
        self.settings.setValue("YY_lineEdit_2", self.ui.YY_lineEdit_2.text())
        self.settings.setValue("YY_spinBox_yspyl_2", self.ui.YY_spinBox_yspyl_2.value())
        self.settings.setValue("YY_spinBox_yyyl_2", self.ui.YY_spinBox_yyyl_2.value())
        self.settings.setValue("CMM_spinBox_3", self.ui.CMM_spinBox_3.value())
        self.settings.setValue("CMM_lineEdit_5", self.ui.CMM_lineEdit_5.text())
        self.settings.setValue("CMM_lineEdit_6", self.ui.CMM_lineEdit_6.text())
        self.settings.setValue("spinBox_3", self.ui.spinBox_3.value())

        # 保存LK_lineEdit_3设置
        self.settings.setValue("LK_lineEdit_3", self.ui.LK_lineEdit_3.text())

        # 保存YY_lineEdit_2设置
        self.settings.setValue("YY_lineEdit_2", self.ui.YY_lineEdit_2.text())

        # 保存随机混剪tab设置
        self.settings.setValue("SJHJSC_lineEdit_6", self.ui.SJHJSC_lineEdit_6.text())
        self.settings.setValue("SJHJ_spinBox_4", self.ui.SJHJ_spinBox_4.value())
        self.settings.setValue("SJHJZC_checkBox_2", self.ui.SJHJZC_checkBox_2.isChecked())
        self.settings.setValue("SJHJZC_comboBox_2", self.ui.SJHJZC_comboBox_2.currentText())
        self.settings.setValue("SJHJZC_doubleSpinBox_3", self.ui.SJHJZC_doubleSpinBox_3.value())
        self.settings.setValue("CMM_checkBox_9", self.ui.CMM_checkBox_9.isChecked())
        self.settings.setValue("CMM_spinBox_4", self.ui.CMM_spinBox_4.value())
        self.settings.setValue("CMM_lineEdit_8", self.ui.CMM_lineEdit_8.text())
        self.settings.setValue("CMM_lineEdit_7", self.ui.CMM_lineEdit_7.text())
        self.settings.setValue("CMM_checkBox_8", self.ui.CMM_checkBox_8.isChecked())
        self.settings.setValue("spinBox_4", self.ui.spinBox_4.value())



    def _select_music_folder(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择音乐文件夹")
        if dir_path:
            self.ui.YY_lineEdit.setText(dir_path)
            self._save_settings()

    def add_main_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择前贴视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_files(files, self.qt_model, self.main_files)

    def remove_selected_main_files(self):
        indexes = self.ui.QT_listView.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.main_files.pop(idx.row())
            self.qt_model.setStringList([Path(f).name for f in self.main_files])

    def clear_main_files(self):
        self.main_files = []
        self.qt_model.setStringList([])

    def add_variant_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择后贴视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_files(files, self.ht_model, self.variant_files)

    def remove_selected_variant_files(self):
        indexes = self.ui.HT_listView_2.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.variant_files.pop(idx.row())
            self.ht_model.setStringList([Path(f).name for f in self.variant_files])

    def clear_variant_files(self):
        self.variant_files = []
        self.ht_model.setStringList([])

    def select_output_directory(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.ui.SC_lineEdit.setText(dir_path)

    def run_initial_check(self):
        # 验证前贴、后贴和输出目录
        if not self.main_files:
            QMessageBox.warning(self, "错误", "请先添加前贴素材！")
            return
        if not self.variant_files:
            QMessageBox.warning(self, "错误", "请先添加后贴素材！")
            return
        if not self.output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 准备文件路径
        self.mixer.main_files = [Path(f) for f in self.main_files]
        self.mixer.variant_files = [Path(f) for f in self.variant_files]
        self.mixer.output_dir = Path(self.output_dir)

        # 设置转场配置 - 每次初始化检查时都重新读取最新设置
        if self.ui.ZC_checkBox.isChecked():
            transition_type = self._parse_transition_type(self.ui.ZC_comboBox.currentText())
            transition_duration = self.ui.ZC_doubleSpinBox.value()
            self.mixer.transition_config = {
                'enabled': True,
                'type': transition_type,
                'duration': transition_duration
            }
            self.update_log(f"✅ 转场配置已启用: {transition_type}, 时长: {transition_duration}秒")
            self.update_log(f"🔧 转场复选框状态: {self.ui.ZC_checkBox.isChecked()}")
            self.update_log(f"🔧 转场类型: {self.ui.ZC_comboBox.currentText()}")
            self.update_log(f"🔧 转场时长: {self.ui.ZC_doubleSpinBox.value()}秒")
        else:
            self.mixer.transition_config = {'enabled': False}
            self.update_log("ℹ️ 转场功能未勾选，将使用简单拼接")

        # 执行初始化检查
        if not self.mixer.find_ffmpeg():
            QMessageBox.critical(self, "错误", "未找到 FFmpeg！")
            return

        if not self.mixer.validate_files(self.mixer.main_files, "前贴"):
            return
        if not self.mixer.validate_files(self.mixer.variant_files, "后贴"):
            return

        if not self.mixer.check_video_format(self.mixer.main_files, "前贴"):
            QMessageBox.warning(
                self, "警告", "前贴素材存在格式不一致，可能导致合并失败！"
            )
        if not self.mixer.check_video_format(self.mixer.variant_files, "后贴"):
            QMessageBox.warning(
                self, "警告", "后贴素材存在格式不一致，可能导致合并失败！"
            )

        # 生成策略
        self.mixer.k = self.mixer.determine_optimal_k(
            len(self.mixer.main_files), len(self.mixer.variant_files)
        )
        self.mixer.strategies = self.mixer.generate_strategies()
        self._display_strategies()

        # 启用开始按钮
        self.ui.KS_pushButton.setEnabled(True)

    def _display_strategies(self):
        # 清空现有按钮
        for btn in self.strategy_buttons:
            btn.setParent(None)
        self.strategy_buttons = []

        # 添加新按钮
        for idx, (name, count) in enumerate(self.mixer.strategies, 1):
            btn = QRadioButton(f"{idx}. {name}（预计生成 {count} 个视频）")
            btn.clicked.connect(lambda _, s=name: self._select_strategy(s))
            self.strategy_layout.addWidget(btn)
            self.strategy_buttons.append(btn)

    def _select_strategy(self, strategy):
        self.selected_strategy = strategy
        self.update_log(f"已选择策略：{strategy}")

    def start_mix(self):
        if not self.selected_strategy:
            QMessageBox.warning(self, "错误", "请先选择策略！")
            return

        self.is_running = True
        self.ui.KS_pushButton.setEnabled(False)
        self.ui.TZ_pushButton.setEnabled(True)

        # 记录原始文件列表（只记录.mp4文件）
        self.original_files = {}
        self.modified_files = {}  # 初始化 modified_files 字典

        if self.output_dir:
            for f in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, f)
                if os.path.isfile(file_path) and os.path.splitext(f)[1].lower() == '.mp4':
                    self.original_files[file_path] = os.path.splitext(f)[1]

        # 修改所有原始视频文件扩展名为.MP，避免处理
        if self.original_files:
            self.update_log("开始标记输出目录中视频文件")
            for original_file, _ in self.original_files.items():
                modified_file = os.path.splitext(original_file)[0] + ".MP"
                try:
                    os.rename(original_file, modified_file)
                    self.modified_files[modified_file] = original_file  # 记录修改后的文件与原始文件的映射
                    self.update_log(f"✅ 重命名：{original_file} → {modified_file}")
                except Exception as e:
                    self.update_log(f"❌ 重命名失败：{original_file} → {modified_file}，错误：{str(e)[:50]}")

        self.mixer.selected_strategy = self.selected_strategy

        # 传递转场配置到mixer - 保持原始类型，在mixer中解析随机
        if self.ui.ZC_checkBox.isChecked():
            transition_type = self.ui.ZC_comboBox.currentText()
            transition_duration = self.ui.ZC_doubleSpinBox.value()

            self.mixer.transition_config = {
                'enabled': True,
                'type': transition_type,  # 保持原始类型，不在这里解析随机
                'duration': transition_duration
            }
            self.update_log(f"✅ 转场配置已传递到混剪器: {transition_type}, {transition_duration}秒")
        else:
            self.mixer.transition_config = {'enabled': False}
            self.update_log("ℹ️ 转场功能未启用")

        self.mixing_thread = MixingThread(self.mixer)
        self.mixing_thread.log_signal.connect(self.update_log)
        self.mixing_thread.progress_signal.connect(self.update_progress)
        self.mixing_thread.finished.connect(self.on_mixing_finished)
        self.mixing_thread.start()

    def stop_mix(self):
        """停止混剪和所有扩展功能"""
        self.mixer.stop()
        self.is_running = False  # 设置全局停止标志

        # 停止混剪线程
        if hasattr(self, 'mixing_thread') and self.mixing_thread.isRunning():
            self.mixing_thread.terminate()

        # 重置按钮状态
        self.ui.KS_pushButton.setEnabled(True)
        self.ui.TZ_pushButton.setEnabled(False)
        self.ui.progressBar.setValue(0)

        QMessageBox.information(self, "提示", "混剪任务已停止")

    def update_log(self, message):
        self.ui.CL_plainTextEdit.appendPlainText(message)

    def add_files_from_drag(self, files, list_type):
        """处理拖放添加的文件"""
        print("【调试】拖放的文件完整路径列表：", files)  # 添加调试打印
        video_files = [f for f in files if self._is_video_file(f)]
        print("【调试】筛选后的视频文件路径列表：", video_files)  # 添加调试打印
        if not video_files:
            QMessageBox.warning(self, "提示", "请拖放视频文件！")
            return

        if list_type == "main":
            self._add_files(video_files, self.qt_model, self.main_files)
        elif list_type == "variant":
            self._add_files(video_files, self.ht_model, self.variant_files)
        elif list_type == "frame":
            self._add_frame_files(video_files)
        elif list_type == "hj":
            self._add_hj_files(video_files)
        elif list_type == "lk":
            self._add_lk_files(video_files)
        elif list_type == "sjhj":
            self._add_sjhj_files(video_files)

    def _add_files(self, files, model, target_list):
        """通用添加文件方法，同步更新模型和目标列表"""
        current_files = [f for f in model.stringList()]  # 转换为普通列表
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            target_list.extend(new_files)
            model.setStringList(current_files)
        #   QMessageBox.information(self, "成功", f"已添加 {len(new_files)} 个文件")
        # else:
        #     QMessageBox.information(
        #         self, "提示", "没有新文件被添加（已存在或非视频文件）"
        #     )

    def _is_video_file(self, file_path):
        video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv"]
        return Path(file_path).suffix.lower() in video_extensions

    def update_progress(self, current, total):
        progress = int((current / total) * 100)
        self.ui.progressBar.setValue(progress)

    def batch_rename(self, files):
        # 这里添加批量重命名的具体逻辑
        processed_files = []
        for file in files:
            # 示例：简单地在文件名前添加 "renamed_"
            base_name = os.path.basename(file)
            new_name = f"renamed_{base_name}"
            new_path = os.path.join(os.path.dirname(file), new_name)
            try:
                os.rename(file, new_path)
                self.update_log(f"✅ 重命名：{file} → {new_path}")
                processed_files.append(new_path)
            except Exception as e:
                self.update_log(f"❌ 重命名失败：{str(e)[:50]}")
                processed_files.append(file)
        return processed_files

    def on_mixing_finished(self):
        # 不要在这里设置 is_running = False，让扩展功能继续执行
        # self.is_running = False  # 注释掉这行

        # 不要在这里重置按钮状态，等所有功能完成后再重置
        # self.ui.KS_pushButton.setEnabled(True)
        # self.ui.TZ_pushButton.setEnabled(False)

        # 初始化文件跟踪字典
        self.file_tracker = {}

        # 获取混剪后新生成的文件列表（排除原始文件）
        mixed_files = []
        if self.output_dir:
            all_files = [
                os.path.join(self.output_dir, f)
                for f in os.listdir(self.output_dir)
                if os.path.isfile(os.path.join(self.output_dir, f))
            ]

            # 区分原始文件和混剪文件
            for file in all_files:
                # 检查是否是原始文件的修改版本
                is_original_file = False
                if hasattr(self, 'modified_files') and self.modified_files:
                    is_original_file = file in self.modified_files.values()

                if is_original_file:  # 是原始文件的修改版本
                    self.file_tracker[file] = {
                        'type': 'original',
                        'status': 'inactive',  # 原始文件暂时标记为不活跃
                        'history': [file]
                    }
                elif os.path.splitext(file)[1].lower() == '.mp4':  # 是新生成的混剪文件
                    self.file_tracker[file] = {
                        'type': 'mixed',
                        'status': 'active',
                        'history': [file]
                    }
                    mixed_files.append(file)

        # 计算扩展功能的数量
        extension_count = sum(
            [
                self.ui.ZC_checkBox.isChecked(),
                self.ui.CZ_checkBox.isChecked(),
                self.ui.YY_checkBox_2.isChecked(),
                self.ui.CMM_checkBox_2.isChecked(),
            ]
        )
        # 计算进度条的分配 - 统一进度条显示
        total_steps = 1 + extension_count  # 混剪 + 扩展功能
        step_progress = 100 / total_steps if total_steps > 0 else 100
        current_step = 1  # 混剪完成算第1步

        # 设置混剪完成后的进度
        self.ui.progressBar.setValue(int(current_step * step_progress))

        # 执行扩展功能（只处理混剪生成的文件）
        # 按照指定顺序执行：转场 -> 抽帧 -> 背景音乐 -> 批量重命名
        if self.ui.ZC_checkBox.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行转场扩展功能")
            mixed_files = self.apply_transitions(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        if self.ui.CZ_checkBox.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行抽帧去重扩展功能")
            mixed_files = self.process_frame_removal(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        if self.ui.YY_checkBox_2.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行添加背景音乐扩展功能")
            mixed_files = self.add_background_music(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        if self.ui.CMM_checkBox_2.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行批量重命名扩展功能")
            mixed_files = self.batch_rename(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        # 恢复原始文件扩展名
        if hasattr(self, 'modified_files') and self.modified_files:
            for modified_file, original_file in self.modified_files.items():
                if os.path.exists(modified_file):
                    try:
                        os.rename(modified_file, original_file)
                        self.file_tracker[original_file] = {
                            'type': 'original',
                            'status': 'active',
                            'history': [modified_file, original_file]
                        }
                        self.update_log(f"✅ 恢复扩展名：{modified_file} → {original_file}")
                    except Exception as e:
                        self.update_log(f"❌ 恢复扩展名失败：{modified_file} → {original_file}，错误：{str(e)[:50]}")

        # 确定需要保留的最终文件
        files_to_keep = []

        # 添加原始文件
        for file, info in self.file_tracker.items():
            if info['type'] == 'original' and info['status'] == 'active':
                files_to_keep.append(file)

        # 添加最新处理的混剪文件
        for file, info in self.file_tracker.items():
            if info['type'] == 'mixed' and info['status'] == 'active':
                latest_version = info['history'][-1]
                if latest_version not in files_to_keep:
                    files_to_keep.append(latest_version)

        # 删除中间文件
        all_files = [os.path.join(self.output_dir, f) for f in os.listdir(self.output_dir) if os.path.isfile(os.path.join(self.output_dir, f))]

        # 添加调试信息
        self.update_log(f"🔍 文件清理调试信息:")
        self.update_log(f"   输出目录中的所有文件: {len(all_files)} 个")
        self.update_log(f"   需要保留的文件: {len(files_to_keep)} 个")

        deleted_count = 0
        for file in all_files:
            if file not in files_to_keep:
                try:
                    os.remove(file)
                    self.update_log(f"✅ 删除中间文件：{os.path.basename(file)}")
                    deleted_count += 1
                except Exception as e:
                    self.update_log(f"❌ 删除中间文件 {os.path.basename(file)} 失败：{str(e)[:50]}")

        if deleted_count == 0:
            self.update_log("ℹ️ 没有中间文件需要删除")
        else:
            self.update_log(f"🧹 共删除 {deleted_count} 个中间文件")

        # 所有任务完成后重置状态和显示完成提示
        self.is_running = False
        self.ui.KS_pushButton.setEnabled(True)
        self.ui.TZ_pushButton.setEnabled(False)
        self.ui.progressBar.setValue(100)

        QMessageBox.information(self, "成功", "所有任务完成！")


    def _update_file_tracker(self, processed_files):
        """更新文件跟踪器，确保正确记录文件处理历史"""
        for new_file in processed_files:
            # 查找该文件的原始版本
            found = False
            for file, info in self.file_tracker.items():
                if new_file in info['history']:
                    found = True
                    break

            if not found:
                # 查找是否是已有文件的更新版本
                base_name = os.path.basename(new_file)
                # 使用正则表达式匹配可能的文件名模式
                for file, info in self.file_tracker.items():
                    if info['type'] == 'mixed':
                        old_base = os.path.basename(file)
                        # 检查是否是同一文件的不同版本
                        if re.search(r'merged_\d+_\d+', old_base) and re.search(r'merged_\d+_\d+', base_name):
                            # 属于同一混剪文件的不同版本
                            info['history'].append(new_file)
                            # 更新文件跟踪器中的条目
                            self.file_tracker.pop(file)
                            self.file_tracker[new_file] = info
                            found = True
                            break
                        elif old_base in base_name or base_name in old_base:
                            # 基于名称相似性判断
                            info['history'].append(new_file)
                            self.file_tracker.pop(file)
                            self.file_tracker[new_file] = info
                            found = True
                            break

            if not found:
                # 全新的文件（可能是用户手动添加的）
                self.file_tracker[new_file] = {
                    'type': 'mixed',
                    'status': 'active',
                    'history': [new_file]
                }

    # 整体添加背景音乐功能
    def add_background_music(self, files):
        processed_files = []

        for file in files:
            try:
                base_name = os.path.splitext(os.path.basename(file))[0]
                new_file_name = f"{base_name}_with_music.mp4"
                new_file_path = os.path.join(os.path.dirname(file), new_file_name)

                self.update_log(f"开始处理文件：{file}")

                # 获取音乐文件夹
                music_folder = self.ui.YY_lineEdit.text()
                if not music_folder or not os.path.exists(music_folder):
                    self.update_log("❌ 音乐文件夹不存在或未选择")
                    processed_files.append(file)
                    continue

                # 获取音乐文件列表 - 支持更多音频格式
                music_files = [
                    f for f in os.listdir(music_folder)
                    if f.lower().endswith(('.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg')) and os.path.isfile(os.path.join(music_folder, f))
                ]

                if not music_files:
                    self.update_log("❌ 音乐文件夹中无可用音乐文件")
                    self.update_log(f"   检查的文件夹：{music_folder}")
                    self.update_log(f"   支持的格式：mp3, wav, aac, m4a, flac, ogg")
                    processed_files.append(file)
                    continue

                # 随机选择音乐
                selected_music = random.choice(music_files)
                music_path = os.path.join(music_folder, selected_music)
                self.update_log(f"🎵 选择音乐：{selected_music}")

                # 获取视频时长
                duration_cmd = [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    file
                ]

                try:
                    result = subprocess.run(
                        duration_cmd,
                        capture_output=True,
                        text=True,
                        check=True,
                        encoding='utf-8',
                        creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
                    )
                    video_duration = float(result.stdout.strip())
                except Exception as e:
                    self.update_log(f"❌ 获取视频时长失败：{str(e)[:50]}")
                    processed_files.append(file)
                    continue

                # 设置音量
                music_volume = self.ui.YY_spinBox_yyyl.value() / 10.0  # 转换为小数
                video_volume = self.ui.YY_spinBox_yspyl.value() / 10.0  # 转换为小数

                # 检查音量设置
                if music_volume <= 0:
                    music_volume = 0.6  # 默认音乐音量
                    self.update_log(f"⚠️ 音乐音量为0，使用默认值：{music_volume}")
                if video_volume <= 0:
                    video_volume = 0.8  # 默认视频音量
                    self.update_log(f"⚠️ 视频音量为0，使用默认值：{video_volume}")

                self.update_log(f"🔊 音量设置 - 视频：{video_volume}, 音乐：{music_volume}")

                # 检查视频是否有音频流
                has_video_audio = self._check_audio_stream(file)

                # 构建FFmpeg命令
                if has_video_audio:
                    # 视频有音频，混合原音频和背景音乐
                    ffmpeg_cmd = [
                        "ffmpeg",
                        "-i", file,
                        "-i", music_path,
                        "-filter_complex",
                        f"[0:a]volume={video_volume}[v0];[1:a]aloop=loop=-1:size=2e+09,volume={music_volume}[v1];[v0][v1]amix=inputs=2:duration=first:normalize=0[aout]",
                        "-map", "0:v:0",
                        "-map", "[aout]",
                        "-c:v", "copy",
                        "-c:a", "aac",
                        "-b:a", "256k",
                        "-ar", "48000",
                        "-shortest",
                        "-y",
                        new_file_path
                    ]
                    self.update_log(f"🎵 混合原音频和背景音乐")
                else:
                    # 视频没有音频，只添加背景音乐
                    ffmpeg_cmd = [
                        "ffmpeg",
                        "-i", file,
                        "-i", music_path,
                        "-filter_complex",
                        f"[1:a]aloop=loop=-1:size=2e+09,volume={music_volume}[aout]",
                        "-map", "0:v:0",
                        "-map", "[aout]",
                        "-c:v", "copy",
                        "-c:a", "aac",
                        "-b:a", "256k",
                        "-ar", "48000",
                        "-shortest",
                        "-y",
                        new_file_path
                    ]
                    self.update_log(f"🎵 为无音频视频添加背景音乐")

                # 执行命令
                result = subprocess.run(
                    ffmpeg_cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
                )

                if result.returncode != 0:
                    self.update_log(f"❌ 失败：{result.stderr[:100]}")
                    processed_files.append(file)
                else:
                    self.update_log(f"✅ 完成：{file} → {new_file_path}")
                    processed_files.append(new_file_path)

            except Exception as e:
                self.update_log(f"❌ 失败：{str(e)[:100]}")
                processed_files.append(file)

        return processed_files


    # 抽帧去重功能
    def process_frame_removal(self, files):
        print("进入 process_frame_removal 方法")
        DELETE_FRAMES_MIN = self.ui.CZ_spinBox_low.value()
        DELETE_FRAMES_MAX = self.ui.CZ_spinBox_max.value()
        processed_files = []
        total_files = len(files)
        for i, file in enumerate(files):
            input_path = file
            safe_name = sanitize_filename(os.path.basename(file))
            # 确保输出文件名包含 .mp4 扩展名
            output_name = f"{os.path.splitext(safe_name)[0]}_processed.mp4"
            output_path = os.path.join(self.output_dir, output_name)

            if os.path.exists(output_path):
                continue

            meta = get_video_metadata(input_path)
            if not meta:
                self.update_log(f"❌ {file} 元数据解析失败，详细原因请检查控制台输出")
                continue

            # 检查 nb_frames 键是否存在
            if 'nb_frames' not in meta.get('streams', [{}])[0]:
                self.update_log(f"❌ {file} 无法获取帧数信息，元数据内容：{meta}")
                continue

            total_frames = int(meta['streams'][0]['nb_frames'])
            delete_frames = random.randint(DELETE_FRAMES_MIN, DELETE_FRAMES_MAX)
            if total_frames < delete_frames:
                self.update_log(
                    f"❌ {file} 帧数不足（{total_frames} < {delete_frames}）"
                )
                continue

            frame_ids = sorted(random.sample(range(total_frames), delete_frames))
            frame_duration = float(meta["format"]["duration"]) / total_frames
            time_expr = [
                f"between(t,{f*frame_duration:.6f},{(f+1)*frame_duration:.6f})"
                for f in frame_ids
            ]

            # 构建基础FFmpeg命令
            base_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-hide_banner",
                "-vf",
                f"select='not({'+'.join(time_expr)})',setpts=N/FRAME_RATE/TB",
                "-af",
                f"aselect='not({'+'.join(time_expr)})',asetpts=N/SR/TB",
                "-c:v", "placeholder",  # 将被GPU函数替换
                "-b:v",
                VIDEO_BITRATE,
                "-maxrate",
                "18000k",
                "-bufsize",
                "36000k",
                "-c:a",
                "aac",
                "-b:a",
                "320k",
                "-y",
                output_path
            ]

            # 使用统一的GPU加速函数
            cmd, using_gpu = build_gpu_ffmpeg_cmd(base_cmd)
            if using_gpu:
                self.update_log("🚀 使用GPU硬件加速处理")
            else:
                self.update_log("🔄 使用CPU处理")

            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=1800,
                    creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
                )
                if result.returncode != 0:
                    self.update_log(f"❌ 失败：{result.stderr}，执行的命令：{' '.join(cmd)}")
                else:
                    output_meta = get_video_metadata(output_path)
                    output_frames = int(output_meta["streams"][0]["nb_frames"]) if output_meta and 'nb_frames' in output_meta.get('streams', [{}])[0] else 0
                    self.update_log(
                        f"✅ 完成：{file}（原帧：{total_frames} → 新帧：{output_frames} 丨 抽帧：{total_frames - output_frames}）"
                    )
                    processed_files.append(output_path)
            except subprocess.TimeoutExpired:
                self.update_log(f"❌ 失败：执行超时，命令：{' '.join(cmd)}")
            except Exception as e:
                self.update_log(f"❌ 失败：{str(e)}，执行的命令：{' '.join(cmd)}")

            # 不在这里更新进度条，由外部统一管理

        return processed_files



    # 转场功能
    def apply_transitions(self, files):
        """为视频文件添加转场特效"""
        if not self.ui.ZC_checkBox.isChecked():
            return files

        # tab_QT的转场功能应该在混剪过程中应用，而不是后处理
        # 如果用户勾选了转场扩展功能，说明转场配置没有正确传递到混剪过程
        self.update_log("ℹ️ tab_QT的转场功能应该在混剪过程中应用")

        # 检查转场是否已在混剪中应用
        if hasattr(self.mixer, 'transition_config') and self.mixer.transition_config.get('enabled', False):
            self.update_log("✅ 转场功能已在混剪过程中应用，跳过后处理转场")
            return files
        else:
            self.update_log("⚠️ 转场配置未正确传递到混剪过程，请检查转场设置")
            self.update_log("💡 提示：tab_QT的转场应该在混剪时应用，而不是后处理")
            return files

    def _parse_transition_type(self, transition_text):
        """解析转场类型文本，返回FFmpeg转场效果名称"""
        # 完整的转场映射表，包含所有42个选项
        transition_map = {
            "【【全随机】】": "random_all",
            "【【柔 · 随机】】": "random_soft",
            "【【硬 · 随机】】": "random_hard",
            "【柔】【叠化】fade": "fade",
            "【向左擦除】wipeleft": "wipeleft",
            "【向右擦除】wiperight": "wiperight",
            "【向上擦除】wipeup": "wipeup",
            "【向下擦除】wipedown": "wipedown",
            "【柔】【向左擦除】smoothleft": "smoothleft",
            "【柔】【向右擦除】smoothright": "smoothright",
            "【柔】【向上擦除】smoothup": "smoothup",
            "【柔】【向下擦除】smoothdown": "smoothdown",
            "【向左滑动】slideleft": "slideleft",
            "【向右滑动】slideright": "slideright",
            "【向上滑动】slideup": "slideup",
            "【向下滑动】slidedown": "slidedown",
            "【柔】【圆形展开】circleopen": "circleopen",
            "【柔】【圆形闭合】circleclose": "circleclose",
            "【柔】【垂直展开】vertopen": "vertopen",
            "【柔】【垂直闭合】vertclose": "vertclose",
            "【柔】【水平展开】horzopen": "horzopen",
            "【柔】【水平闭合】horzclose": "horzclose",
            "【柔】【景深转场】distance": "distance",
            "【时钟擦除】radial": "radial",
            "【像素模糊】pixelize": "pixelize",
            "【放大转场】zoomin": "zoomin",
            "【柔】【向左上擦除】diagtl": "diagtl",
            "【柔】【向右上擦除】diagtr": "diagtr",
            "【柔】【向左下擦除】diagbl": "diagbl",
            "【柔】【向右下擦除】diagbr": "diagbr",
            "【向左上擦除】wipetl": "wipetl",
            "【向右上擦除】wipetr": "wipetr",
            "【向左下擦除】wipebl": "wipebl",
            "【向右下擦除】wipebr": "wipebr",
            "【向左百叶窗】hlslice": "hlslice",
            "【向右百叶窗】hrslice": "hrslice",
            "【向上百叶窗】vuslice": "vuslice",
            "【向下百叶窗】vdslice": "vdslice",
            "【向左滑刺】hlwind": "hlwind",
            "【向右滑刺】hrwind": "hrwind",
            "【向上滑刺】vuwind": "vuwind",
            "【向下滑刺】vdwind": "vdwind"
        }

        # 对于随机转场，返回标识符而不是具体转场效果
        # 这样在_merge_with_transitions中可以正确处理每段都随机的逻辑

        return transition_map.get(transition_text, "fade")

    def _merge_videos_with_transition(self, videos, output_path, transition, duration):
        """使用FFmpeg合并视频并添加转场效果"""
        if len(videos) < 2:
            return False

        try:
            # 处理随机转场
            if transition.startswith("random"):
                if transition == "random_all":
                    # 全随机：从所有转场中选择 - 包含用户要求的42个特效
                    all_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                                     "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                                     "distance", "diagtl", "diagtr", "diagbl", "diagbr", "wipeleft", "wiperight",
                                     "wipeup", "wipedown", "slideleft", "slideright", "slideup", "slidedown",
                                     "radial", "pixelize", "zoomin", "wipetl", "wipetr", "wipebl", "wipebr",
                                     "hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]
                    transition = random.choice(all_transitions)
                elif transition == "random_soft":
                    # 柔随机：只从柔和转场中选择 - 16个柔和特效
                    soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                                      "circleopen", "circleclose", "vertopen", "vertclose",
                                      "horzopen", "horzclose", "distance", "diagtl", "diagtr", "diagbl", "diagbr"]
                    transition = random.choice(soft_transitions)
                elif transition == "random_hard":
                    # 硬随机：从硬切转场中选择 - 20个硬切特效
                    hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright",
                                      "slideup", "slidedown", "radial", "pixelize", "zoomin", "wipetl", "wipetr",
                                      "wipebl", "wipebr", "hlslice", "hrslice", "vuslice", "vdslice",
                                      "hlwind", "hrwind", "vuwind", "vdwind"]
                    transition = random.choice(hard_transitions)

            # 构建FFmpeg命令
            inputs = []
            for video in videos:
                inputs.extend(["-i", video])

            # 构建滤镜复合体
            filter_complex = []

            # 获取第一个视频的分辨率作为目标分辨率
            try:
                from utils import get_video_metadata
                first_video_meta = get_video_metadata(videos[0])
                if first_video_meta and 'streams' in first_video_meta:
                    video_stream = first_video_meta['streams'][0]
                    target_width = video_stream.get('width', 1920)
                    target_height = video_stream.get('height', 1080)
                else:
                    target_width, target_height = 1920, 1080
            except:
                target_width, target_height = 1920, 1080

            self.update_log(f"使用目标分辨率: {target_width}x{target_height}")

            # 为每个视频添加预处理 - 使用素材的原始分辨率
            for i in range(len(videos)):
                filter_complex.append(f"[{i}:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v{i}]")

            # 添加转场效果 - 修复引号和offset计算
            current_output = "v0"
            cumulative_offset = 0.0

            for i in range(1, len(videos)):
                # 获取前一个视频的时长来计算正确的offset
                try:
                    from utils import get_video_duration
                    prev_duration = get_video_duration(videos[i-1])
                    if prev_duration > 0:
                        # 确保转场时长不超过前一个视频时长的一半
                        safe_duration = min(duration, prev_duration * 0.5)
                        # 计算转场开始的offset（前一个视频结束前safe_duration秒开始转场）
                        offset = cumulative_offset + prev_duration - safe_duration
                        cumulative_offset += prev_duration - safe_duration
                    else:
                        safe_duration = duration
                        offset = cumulative_offset
                        cumulative_offset += safe_duration
                except:
                    # 如果无法获取时长，使用默认值
                    safe_duration = duration
                    offset = cumulative_offset
                    cumulative_offset += safe_duration

                next_output = f"v{i}" if i == len(videos) - 1 else f"trans{i}"
                # 使用单引号包围转场名称，修复offset计算
                filter_complex.append(f"[{current_output}][v{i}]xfade=transition='{transition}':duration={safe_duration:.3f}:offset={offset:.3f}[{next_output}]")
                current_output = next_output

            # 音频处理 - 修复音频呲呲啦啦声音
            audio_inputs = []
            for i in range(len(videos)):
                # 为每个音频流添加音量控制，避免音频失真
                audio_inputs.append(f"[{i}:a]volume=0.8[a{i}]")

            # 使用修复后的音频混合参数
            audio_mix_inputs = "".join([f"[a{i}]" for i in range(len(videos))])
            filter_complex.extend(audio_inputs)
            filter_complex.append(f"{audio_mix_inputs}amix=inputs={len(videos)}:duration=first:normalize=0[aout]")

            filter_str = ";".join(filter_complex)

            cmd = [
                "ffmpeg",
                *inputs,
                "-filter_complex", filter_str,
                "-map", f"[{current_output}]",
                "-map", "[aout]",
                "-c:v", "libx264",
                "-preset", "slow",
                "-crf", "15",
                "-c:a", "aac",
                "-y",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=3600,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                return True
            else:
                self.update_log(f"❌ FFmpeg错误：{result.stderr[:200]}")
                return False

        except Exception as e:
            self.update_log(f"❌ 转场处理异常：{str(e)[:100]}")
            return False

    # 批量重命名功能
    def batch_rename(self, files):
        start_value = self.ui.CMM_spinBox.value()
        prefix = self.ui.CMM_lineEdit.text()
        suffix = self.ui.CMM_lineEdit_2.text()
        fill_zeros = self.ui.spinBox.value()
        keep_extension = self.ui.CMM_checkBox_3.isChecked()

        # 需要跳过的临时扩展名列表
        temp_extensions = ['.mp']

        total_files = len(files)  # 定义文件总数
        processed_files = []
        renamed_conflicts = []  # 记录重命名冲突的文件

        for i, file in enumerate(files, start=start_value):
            # 获取文件完整路径的扩展名
            file_ext = os.path.splitext(file)[1]

            # 检查文件扩展名是否是需要跳过的临时扩展名
            if file_ext.lower() in temp_extensions:
                self.update_log(f"⏩ 跳过临时扩展名文件：{file}")
                processed_files.append(file)
                continue

            # 获取文件所在目录
            file_dir = os.path.dirname(file)

            # 构建新文件名，保留原始扩展名或使用用户指定的
            base_name = os.path.basename(file)
            original_ext = os.path.splitext(base_name)[1] if keep_extension else ""
            new_name = f"{prefix}{str(i).zfill(fill_zeros)}{suffix}{original_ext}"

            # 使用原始文件的目录构建新路径
            new_path = os.path.join(file_dir, new_name)

            # 检查文件名冲突（包括被临时重命名为.MP的原始文件）
            conflict_detected = False

            # 检查当前是否存在同名文件
            if os.path.exists(new_path) and new_path != file:
                conflict_detected = True

            # 检查是否与被临时重命名的原始文件冲突
            if not conflict_detected and hasattr(self, 'modified_files') and self.modified_files:
                # 构建对应的.MP文件路径
                mp_path = os.path.splitext(new_path)[0] + ".MP"
                if mp_path in self.modified_files:
                    conflict_detected = True
                    self.update_log(f"⚠️ 检测到与原始文件冲突：{new_name}（原始文件已临时重命名为.MP）")

            if conflict_detected:
                # 如果存在冲突，添加_B后缀
                name_without_ext = os.path.splitext(new_name)[0]
                ext = os.path.splitext(new_name)[1]
                conflict_name = f"{name_without_ext}_B{ext}"
                conflict_path = os.path.join(file_dir, conflict_name)

                # 记录冲突信息
                renamed_conflicts.append({
                    'original_intended': new_name,
                    'actual_name': conflict_name
                })

                new_name = conflict_name
                new_path = conflict_path
                self.update_log(f"⚠️ 发现重名文件，新文件已改名为：{new_name}")

            try:
                os.rename(file, new_path)
                self.update_log(f"✅ 重命名：{file} → {new_path}")
                processed_files.append(new_path)
            except Exception as e:
                self.update_log(f"❌ 重命名失败：{str(e)[:50]}")
                processed_files.append(file)  # 失败时保留原文件

            # 更新进度条
            current_progress = int((i - start_value + 1) / total_files * 100)
            self.ui.progressBar.setValue(current_progress)

        # 如果有重命名冲突，显示弹窗提示
        if renamed_conflicts:
            conflict_message = "发现重复命名，以下文件已自动调整：\n\n"
            for conflict in renamed_conflicts:
                conflict_message += f"原计划名称：{conflict['original_intended']}\n"
                conflict_message += f"实际名称：{conflict['actual_name']}\n\n"

            QMessageBox.information(self, "重命名冲突提示", conflict_message)

        return processed_files

    def add_frame_files(self):
        """添加要处理的视频文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_frame_files(files)

    def _add_frame_files(self, files):
        """添加文件到抽帧列表"""
        current_files = [f for f in self.frame_model.stringList()]
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            self.frame_files.extend(new_files)
            self.frame_model.setStringList(current_files)

    def remove_selected_frame_files(self):
        """移除选中的文件"""
        indexes = self.ui.DDCZ_listView_3.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.frame_files.pop(idx.row())
            self.frame_model.setStringList([Path(f).name for f in self.frame_files])

    def clear_frame_files(self):
        """清空文件列表"""
        self.frame_files = []
        self.frame_model.setStringList([])

    def select_frame_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.ui.DDCZ_lineEdit_3.setText(dir_path)

    def on_frame_radio_toggled(self):
        """处理单选按钮状态改变"""
        replace_mode = self.ui.DDCZ_radioButton_TH.isChecked()
        # 禁用/启用相关控件
        self.ui.DDCZ_lineEdit_3.setEnabled(not replace_mode)
        self.ui.DDCZ_where_3.setEnabled(not replace_mode)
        self.ui.DDCZ_groupBox_KZ.setEnabled(not replace_mode)

        # 当选择替换原素材模式时，强制取消勾选 CMM_checkBox_4
        if replace_mode:
            self.ui.CMM_checkBox_4.setChecked(False)

    def start_frame_removal(self):
        """开始抽帧处理"""
        if not self.frame_files:
            QMessageBox.warning(self, "错误", "请先添加要处理的视频文件！")
            return

        replace_mode = self.ui.DDCZ_radioButton_TH.isChecked()
        if not replace_mode and not self.ui.DDCZ_lineEdit_3.text():
            QMessageBox.warning(self, "错误", "请选择输出目录！")
            return

        # 检查输出目录中的文件
        if not replace_mode:
            output_dir = self.ui.DDCZ_lineEdit_3.text()
            files_to_rename = []  # 存储需要重命名的文件信息
            to_process_files = set()  # 存储要处理的文件名（不含路径）
            original_files = []  # 存储在输出目录中找到的原文件
            other_files = []  # 存储其他非处理文件

            # 收集要处理的文件名
            for file in self.frame_files:
                to_process_files.add(os.path.basename(file))

            # 检查输出目录中的所有视频文件
            for file in os.listdir(output_dir):
                if file.lower().endswith('.mp4'):
                    file_path = os.path.join(output_dir, file)
                    # 如果是要处理的文件，加入原文件列表
                    if file in to_process_files:
                        original_files.append(file_path)
                    else:
                        # 如果不是要处理的文件，加入其他文件列表
                        other_files.append(file_path)

            # 如果找到原文件，显示在日志中并询问用户
            if original_files:
                self.update_frame_log("在输出目录中发现以下原文件：")
                for file in original_files:
                    self.update_frame_log(f"- {os.path.basename(file)}")

                msg = QMessageBox()
                msg.setIcon(QMessageBox.Question)
                msg.setWindowTitle("发现原文件")
                msg.setText("在输出目录中发现原文件，是否替换？")
                msg.setInformativeText(f"发现{len(original_files)}个原文件")
                replace_button = msg.addButton("替换", QMessageBox.YesRole)
                skip_button = msg.addButton("不替换", QMessageBox.NoRole)
                cancel_button = msg.addButton("取消", QMessageBox.RejectRole)
                msg.exec()

                clicked_button = msg.clickedButton()
                if clicked_button == cancel_button:
                    self.update_frame_log("⚠️ 用户取消操作")
                    return
                elif clicked_button == replace_button:
                    self.update_frame_log("✅ 用户选择替换原文件")
                    self.no_replace_mode = False
                    # 如果选择替换，将其他文件改为.MP
                    for file_path in other_files:
                        new_path = os.path.join(output_dir, os.path.splitext(os.path.basename(file_path))[0] + ".MP")
                        files_to_rename.append((file_path, new_path))
                else:
                    self.update_frame_log("✅ 用户选择不替换原文件")
                    self.no_replace_mode = True
                    # 如果选择不替换，只将其他文件（非目标文件）改为.MP
                    for file_path in other_files:
                        new_path = os.path.join(output_dir, os.path.splitext(os.path.basename(file_path))[0] + ".MP")
                        files_to_rename.append((file_path, new_path))
            else:
                # 如果没有找到原文件，将所有其他文件加入重命名列表
                self.no_replace_mode = False
                for file_path in other_files:
                    new_path = os.path.join(output_dir, os.path.splitext(os.path.basename(file_path))[0] + ".MP")
                    files_to_rename.append((file_path, new_path))

            # 如果有需要重命名的文件
            if files_to_rename:
                self.update_frame_log("\n以下文件将临时改为.MP：")
                for old_path, _ in files_to_rename:
                    self.update_frame_log(f"- {os.path.basename(old_path)}")

                # 执行重命名
                for old_path, new_path in files_to_rename:
                    try:
                        os.rename(old_path, new_path)
                        self.update_frame_log(f"✅ 重命名文件：{os.path.basename(old_path)} → {os.path.basename(new_path)}")
                    except Exception as e:
                        self.update_frame_log(f"❌ 重命名文件失败：{str(e)[:100]}")
                        # 恢复已经重命名的文件
                        for old_path, new_path in files_to_rename:
                            if os.path.exists(new_path):
                                try:
                                    os.rename(new_path, old_path)
                                except:
                                    pass
                        return

        # 初始化处理器
        from frame_removal import FrameRemovalProcessor
        self.frame_processor = FrameRemovalProcessor()
        self.frame_processor.log_signal.connect(self.update_frame_log)
        self.frame_processor.progress_signal.connect(self.update_frame_progress)

        # 设置参数
        if not replace_mode:
            self.frame_processor.set_output_directory(self.ui.DDCZ_lineEdit_3.text())
        self.frame_processor.set_frame_range(
            self.ui.DDCZ_spinBox_low_2.value(),
            self.ui.DDCZ_spinBox_max_2.value()
        )

        # 禁用按钮
        self.ui.DDCZ_pushButton_2.setEnabled(False)
        self.ui.DDCZ_pushButton_3.setEnabled(True)
        self.ui.DDCZprogressBar_2.setValue(0)

        # 开始处理
        try:
            # 根据用户选择设置replace_original参数
            if not replace_mode:
                # 如果是指定输出目录模式，根据用户选择决定是否替换
                should_replace = clicked_button == replace_button if 'clicked_button' in locals() else False
            else:
                # 如果是替换原素材模式，直接设为True
                should_replace = True

            processed_files = self.frame_processor.process_files(
                self.frame_files,
                replace_original=should_replace
            )

            if processed_files:
                # 如果启用了重命名功能，对处理后的文件进行重命名
                if self.ui.CMM_checkBox_5.isChecked():
                    processed_files = self.frame_rename_files(processed_files)
                QMessageBox.information(self, "成功", "抽帧处理完成！")
            else:
                QMessageBox.warning(self, "警告", "没有文件被成功处理！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理过程中发生错误：{str(e)[:200]}")

        finally:
            # 恢复按钮状态
            self.ui.DDCZ_pushButton_2.setEnabled(True)
            self.ui.DDCZ_pushButton_3.setEnabled(False)

            # 恢复被重命名的文件
            if not replace_mode and files_to_rename:
                self.update_frame_log("\n正在恢复临时文件名...")
                for old_path, new_path in files_to_rename:
                    if os.path.exists(new_path):
                        try:
                            os.rename(new_path, old_path)
                            self.update_frame_log(f"✅ 恢复文件名：{os.path.basename(new_path)} → {os.path.basename(old_path)}")
                        except Exception as e:
                            self.update_frame_log(f"⚠️ 恢复文件名失败：{str(e)[:100]}")

    def stop_frame_removal(self):
        """停止抽帧处理"""
        if self.frame_processor:
            self.frame_processor.stop()
            self.ui.DDCZ_pushButton_2.setEnabled(True)
            self.ui.DDCZ_pushButton_3.setEnabled(False)
            QMessageBox.information(self, "提示", "已停止抽帧处理")

    def update_frame_log(self, message):
        """更新抽帧处理日志"""
        self.ui.DDCZ_plainTextEdit_2.appendPlainText(message)

    def update_frame_progress(self, current, total):
        """更新抽帧处理进度"""
        progress = int((current / total) * 100)
        self.ui.DDCZprogressBar_2.setValue(progress)

    def frame_rename_files(self, files):
        """对抽帧处理后的文件进行重命名

        Args:
            files: 要重命名的文件列表

        Returns:
            处理后的文件列表
        """
        if not files:
            return files

        # 获取重命名设置
        start_value = self.ui.CMM_spinBox_2.value()
        prefix = self.ui.CMM_lineEdit_3.text()
        suffix = self.ui.CMM_lineEdit_4.text()
        fill_zeros = self.ui.spinBox_2.value()
        keep_extension = self.ui.CMM_checkBox_5.isChecked()
        rename_enabled = self.ui.CMM_checkBox_4.isChecked()  # 是否启用批量重命名

        # 创建原始文件名到处理后文件名的映射
        original_names = {}
        for file in self.frame_files:
            original_name = os.path.splitext(os.path.basename(file))[0]
            original_names[original_name] = file

        processed_files = []
        total_files = len(files)

        self.update_frame_log("\n开始重命名处理...")
        for i, file in enumerate(files, start=start_value):
            try:
                # 获取文件路径信息
                file_dir = os.path.dirname(file)
                file_name = os.path.basename(file)

                if rename_enabled:
                    # 启用批量重命名时使用序号命名规则
                    base_name = f"{prefix}{str(i).zfill(fill_zeros)}{suffix}"

                    # 在不替换模式下，检查新名称是否与原文件名相同
                    if hasattr(self, 'no_replace_mode') and self.no_replace_mode:
                        # 查找对应的原始文件
                        processed_base = os.path.splitext(file_name)[0]
                        for orig_name in original_names.keys():
                            if processed_base.endswith(orig_name):
                                # 如果新名称与原文件名相同，添加_CZ后缀
                                if base_name == orig_name:
                                    base_name = f"{base_name}_CZ"
                                break
                else:
                    # 未启用批量重命名时保持原文件名
                    base_name = os.path.splitext(file_name)[0]
                    # 在不替换模式下添加_CZ后缀
                    if hasattr(self, 'no_replace_mode') and self.no_replace_mode:
                        base_name = f"{base_name}_CZ"

                # 添加扩展名
                if keep_extension:
                    new_name = base_name + os.path.splitext(file_name)[1]
                else:
                    new_name = base_name + ".mp4"

                new_path = os.path.join(file_dir, new_name)

                # 检查是否存在同名文件
                if os.path.exists(new_path) and new_path != file:
                    self.update_frame_log(f"⚠️ 文件已存在，跳过：{new_name}")
                    processed_files.append(file)
                    continue

                # 执行重命名
                os.rename(file, new_path)
                self.update_frame_log(f"✅ 重命名：{file_name} → {new_name}")
                processed_files.append(new_path)

            except Exception as e:
                self.update_frame_log(f"❌ 重命名失败：{str(e)[:100]}")
                processed_files.append(file)  # 保留原文件

            # 更新进度
            progress = int((i - start_value + 1) / total_files * 100)
            self.ui.DDCZprogressBar_2.setValue(progress)

        return processed_files

    # ===== tab_HJ 混剪功能实现 =====

    def add_hj_files(self):
        """添加混剪素材文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择混剪素材文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_hj_files(files)

    def _add_hj_files(self, files):
        """添加文件到混剪列表"""
        current_files = [f for f in self.hj_model.stringList()]
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            self.hj_files.extend(new_files)
            self.hj_model.setStringList(current_files)

    def remove_selected_hj_files(self):
        """移除选中的混剪文件"""
        indexes = self.ui.HJ_listView_4.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.hj_files.pop(idx.row())
            self.hj_model.setStringList([Path(f).name for f in self.hj_files])

    def clear_hj_files(self):
        """清空混剪文件列表"""
        self.hj_files = []
        self.hj_model.setStringList([])

    def select_hj_output_directory(self):
        """选择混剪输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.hj_output_dir = dir_path
            self.ui.HJSC_lineEdit_4.setText(dir_path)

    def toggle_hj_advanced_settings(self):
        """显示/隐藏高级设置"""
        is_visible = self.ui.HJ_groupBox_GJ.isVisible()
        self.ui.HJ_groupBox_GJ.setVisible(not is_visible)

        # 更新按钮文本
        if is_visible:
            self.ui.HJ_pushButton_GJ.setText("高级设置 ▼")
        else:
            self.ui.HJ_pushButton_GJ.setText("高级设置 ▲")

    def run_hj_initial_check(self):
        """运行混剪初始化检查"""
        if not self.hj_files:
            QMessageBox.warning(self, "错误", "请先添加混剪素材！")
            return
        if not self.hj_output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 检查视频分辨率和帧率
        self.update_hj_log("开始检查视频分辨率和帧率...")

        resolution_stats = {}
        framerate_stats = {}

        for file in self.hj_files:
            try:
                metadata = get_video_metadata(file)
                if metadata and 'streams' in metadata:
                    video_stream = metadata['streams'][0]
                    width = video_stream.get('width', 0)
                    height = video_stream.get('height', 0)

                    # 获取帧率
                    r_frame_rate = video_stream.get('r_frame_rate', '0/1')
                    if '/' in r_frame_rate:
                        num, den = map(int, r_frame_rate.split('/'))
                        fps = round(num / den, 2) if den != 0 else 0
                    else:
                        fps = float(r_frame_rate)

                    # 统计分辨率
                    resolution = f"{width}x{height}"
                    resolution_stats[resolution] = resolution_stats.get(resolution, 0) + 1

                    # 统计帧率
                    framerate_stats[fps] = framerate_stats.get(fps, 0) + 1

            except Exception as e:
                self.update_hj_log(f"❌ 检查文件失败：{Path(file).name} - {str(e)[:50]}")

        # 输出统计结果
        self.update_hj_log("\n=== 分辨率统计 ===")
        resolution_inconsistent = False
        if len(resolution_stats) > 1:
            resolution_inconsistent = True
            # 找出数量较少的分辨率
            sorted_resolutions = sorted(resolution_stats.items(), key=lambda x: x[1])
            minority_resolutions = [item for item in sorted_resolutions[:-1]]  # 除了最多的那个

            self.update_hj_log("❌ 发现分辨率不统一的文件：")
            for resolution, count in minority_resolutions:
                self.update_hj_log(f"  {resolution}: {count}个文件")
                # 列出具体文件
                for file in self.hj_files:
                    try:
                        metadata = get_video_metadata(file)
                        if metadata and 'streams' in metadata:
                            video_stream = metadata['streams'][0]
                            width = video_stream.get('width', 0)
                            height = video_stream.get('height', 0)
                            if f"{width}x{height}" == resolution:
                                self.update_hj_log(f"    - {Path(file).name}")
                    except:
                        pass
        else:
            self.update_hj_log("✅ 所有文件分辨率统一")

        self.update_hj_log("\n=== 帧率统计 ===")
        framerate_inconsistent = False
        if len(framerate_stats) > 1:
            framerate_inconsistent = True
            # 找出数量较少的帧率
            sorted_framerates = sorted(framerate_stats.items(), key=lambda x: x[1])
            minority_framerates = [item for item in sorted_framerates[:-1]]  # 除了最多的那个

            self.update_hj_log("❌ 发现帧率不统一的文件：")
            for framerate, count in minority_framerates:
                self.update_hj_log(f"  {framerate}fps: {count}个文件")
                # 列出具体文件
                for file in self.hj_files:
                    try:
                        metadata = get_video_metadata(file)
                        if metadata and 'streams' in metadata:
                            video_stream = metadata['streams'][0]
                            r_frame_rate = video_stream.get('r_frame_rate', '0/1')
                            if '/' in r_frame_rate:
                                num, den = map(int, r_frame_rate.split('/'))
                                fps = round(num / den, 2) if den != 0 else 0
                            else:
                                fps = float(r_frame_rate)
                            if fps == framerate:
                                self.update_hj_log(f"    - {Path(file).name}")
                    except:
                        pass
        else:
            self.update_hj_log("✅ 所有文件帧率统一")

        # 检查是否有不一致的情况
        if resolution_inconsistent or framerate_inconsistent:
            self.update_hj_log("\n❌ 检查完成！发现格式不一致，无法进行混剪")
            self.update_hj_log("请确保所有素材的分辨率和帧率完全一致后再进行混剪")

            # 显示警告对话框
            QMessageBox.critical(
                self,
                "格式不一致",
                "发现素材的分辨率或帧率不一致！\n\n"
                "为确保混剪质量，请使用分辨率和帧率完全一致的素材。\n"
                "详细信息请查看日志。"
            )
            return False
        else:
            self.update_hj_log("\n✅ 检查完成！所有素材格式一致，可以进行混剪")
            return True

    def update_hj_log(self, message):
        """更新混剪处理日志"""
        self.ui.CL_plainTextEdit_2.appendPlainText(message)

    # 落款功能
    def add_lk_files(self):
        """添加落款文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择落款文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_lk_files(files)

    def _add_lk_files(self, files):
        """添加文件到落款列表（只能有一个文件）"""
        if files:
            # 只取第一个文件
            file = files[0]
            self.lk_files = [file]
            self.lk_model.setStringList([Path(file).name])

    def clear_lk_files(self):
        """清空落款文件列表"""
        self.lk_files = []
        self.lk_model.setStringList([])

    def select_lk_folder(self):
        """选择落款文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择落款文件夹")
        if dir_path:
            self.ui.LK_lineEdit_3.setText(dir_path)

    def select_hj_music_folder(self):
        """选择tab_HJ的背景音乐文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择音乐文件夹")
        if dir_path:
            self.ui.YY_lineEdit_2.setText(dir_path)

    def on_lk_radio_toggled(self):
        """落款单选按钮切换"""
        if self.ui.LK_radioButton_duo.isChecked():
            # 多个落款模式
            self.ui.LK_lineEdit_3.setEnabled(True)
            self.ui.LK_pushButton_3.setEnabled(True)
            self.ui.LK_listView.setEnabled(False)
            self.ui.LK_in_5.setEnabled(False)
            self.ui.LK_clean_5.setEnabled(False)
        else:
            # 单个落款模式
            self.ui.LK_lineEdit_3.setEnabled(False)
            self.ui.LK_pushButton_3.setEnabled(False)
            self.ui.LK_listView.setEnabled(True)
            self.ui.LK_in_5.setEnabled(True)
            self.ui.LK_clean_5.setEnabled(True)

    def on_hj_transition_toggled(self):
        """转场功能切换"""
        enabled = self.ui.HJ_checkBox_ZC.isChecked()
        self.ui.HJ_comboBox_ZC.setEnabled(enabled)
        self.ui.ZC_doubleSpinBox_2.setEnabled(enabled)

        # 根据转场类型设置单选按钮状态
        self.on_hj_transition_changed()

    def on_hj_transition_changed(self):
        """转场类型改变"""
        transition_type = self.ui.HJ_comboBox_ZC.currentText()

        # 所有转场类型都支持多种/单一模式
        if self.ui.HJ_checkBox_ZC.isChecked():
            self.ui.HJ_radioButton_y.setEnabled(True)
            self.ui.HJ_radioButton_n.setEnabled(True)
        else:
            self.ui.HJ_radioButton_y.setEnabled(False)
            self.ui.HJ_radioButton_n.setEnabled(False)

    def start_hj_mix(self):
        """开始混剪处理"""
        if not self.hj_files:
            QMessageBox.warning(self, "错误", "请先添加混剪素材！")
            return
        if not self.hj_output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 执行初始化检查
        if not self.run_hj_initial_check():
            # 如果格式不一致，不允许开始混剪
            return

        # 检查落款设置
        if self.ui.HJ_checkBox_LK.isChecked():
            if self.ui.LK_radioButton_duo.isChecked():
                lk_folder = self.ui.LK_lineEdit_3.text().strip()
                if not lk_folder or not os.path.exists(lk_folder):
                    QMessageBox.warning(self, "错误", "请选择有效的落款文件夹！")
                    return
            else:
                if not self.lk_files:
                    QMessageBox.warning(self, "错误", "请添加落款文件！")
                    return

        self.hj_is_running = True
        self.ui.HJKS_pushButton_2.setEnabled(False)
        self.ui.HJTZ_pushButton_2.setEnabled(True)
        self.ui.HJ_progressBar_2.setValue(0)

        self.update_hj_log("开始混剪处理...")

        # 启动混剪线程
        self.hj_thread = HJMixingThread(self)
        self.hj_thread.progress_updated.connect(self.ui.HJ_progressBar_2.setValue)
        self.hj_thread.log_updated.connect(self.update_hj_log)
        self.hj_thread.finished.connect(self.on_hj_mix_finished)
        self.hj_thread.start()

    def stop_hj_mix(self):
        """停止混剪处理"""
        if hasattr(self, 'hj_thread') and self.hj_thread.isRunning():
            self.hj_thread.stop()
            self.update_hj_log("用户停止了混剪处理")

    def on_hj_mix_finished(self):
        """混剪处理完成"""
        self.hj_is_running = False
        self.ui.HJKS_pushButton_2.setEnabled(True)
        self.ui.HJTZ_pushButton_2.setEnabled(False)
        self.ui.HJ_progressBar_2.setValue(100)
        self.update_hj_log("混剪处理完成！")

    # ==================== 随机混剪tab相关方法 ====================

    def add_sjhj_files(self):
        """添加前贴视频文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择前贴视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_sjhj_files(files)

    def _add_sjhj_files(self, files):
        """添加文件到随机混剪前贴列表"""
        current_files = [f for f in self.sjhj_model.stringList()]
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            self.sjhj_files.extend(new_files)
            self.sjhj_model.setStringList(current_files)

    def remove_selected_sjhj_files(self):
        """移除选中的前贴文件"""
        indexes = self.ui.SJHJ_listView_5.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.sjhj_files.pop(idx.row())
            self.sjhj_model.setStringList([Path(f).name for f in self.sjhj_files])

    def clear_sjhj_files(self):
        """清空前贴文件列表"""
        self.sjhj_files = []
        self.sjhj_model.setStringList([])

    def select_sjhj_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.sjhj_output_dir = dir_path
            self.ui.SJHJSC_lineEdit_5.setText(dir_path)

    def on_sjhj_material_radio_toggled(self):
        """后贴素材库单选按钮切换"""
        if self.ui.SJHJ_radioButton_M.isChecked():
            # 启用默认后台库相关控件
            self.ui.SJHJ_radioButton_FBL1080.setEnabled(True)
            self.ui.SJHJ_radioButton_FBL720.setEnabled(True)
            self.ui.SJHJ_radioButton_ZL30.setEnabled(True)
            self.ui.SJHJ_radioButton_ZL60.setEnabled(True)
            self.ui.SJHJSC_open.setEnabled(True)
            # 禁用自定义库相关控件
            self.ui.SJHJSC_lineEdit_6.setEnabled(False)
            self.ui.SJHJSC_where_6.setEnabled(False)
        else:
            # 禁用默认后台库相关控件
            self.ui.SJHJ_radioButton_FBL1080.setEnabled(False)
            self.ui.SJHJ_radioButton_FBL720.setEnabled(False)
            self.ui.SJHJ_radioButton_ZL30.setEnabled(False)
            self.ui.SJHJ_radioButton_ZL60.setEnabled(False)
            self.ui.SJHJSC_open.setEnabled(False)
            # 启用自定义库相关控件
            self.ui.SJHJSC_lineEdit_6.setEnabled(True)
            self.ui.SJHJSC_where_6.setEnabled(True)

    def on_sjhj_resolution_changed(self):
        """分辨率选择改变"""
        # 这里可以添加分辨率改变时的逻辑，比如日志记录或验证
        if self.ui.SJHJ_radioButton_FBL1080.isChecked():
            resolution = "1080P"
        else:
            resolution = "720P"
        # 可以在这里添加日志或其他处理
        # print(f"分辨率选择改变为: {resolution}")

    def on_sjhj_framerate_changed(self):
        """帧率选择改变"""
        # 这里可以添加帧率改变时的逻辑，比如日志记录或验证
        if self.ui.SJHJ_radioButton_ZL30.isChecked():
            framerate = "30帧"
        else:
            framerate = "60帧"
        # 可以在这里添加日志或其他处理
        # print(f"帧率选择改变为: {framerate}")

    def open_sjhj_material_folder(self):
        """打开默认后贴素材库目录"""
        import subprocess
        import os

        base_path = r"Z:\投放素材\四季合合\优质素材库【孟剪工具用请勿删除】"

        try:
            if os.path.exists(base_path):
                subprocess.Popen(f'explorer "{base_path}"')
            else:
                QMessageBox.warning(self, "提示", f"目录不存在：{base_path}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开目录：{str(e)}")

    def select_sjhj_custom_folder(self):
        """选择自定义后贴素材库目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择后贴素材库目录")
        if dir_path:
            self.ui.SJHJSC_lineEdit_6.setText(dir_path)
            self._save_settings()  # 保存用户设置

    def get_sjhj_variant_folder(self):
        """获取当前选择的后贴素材库目录"""
        if self.ui.SJHJ_radioButton_M.isChecked():
            # 使用默认后台库
            base_path = r"Z:\投放素材\四季合合\优质素材库【孟剪工具用请勿删除】"

            # 根据分辨率选择
            if self.ui.SJHJ_radioButton_FBL1080.isChecked():
                resolution = "1080P"
            else:
                resolution = "720P"

            # 根据帧率选择
            if self.ui.SJHJ_radioButton_ZL30.isChecked():
                framerate = "30帧"
            else:
                framerate = "60帧"

            return os.path.join(base_path, resolution, framerate)
        else:
            # 使用自定义库
            return self.ui.SJHJSC_lineEdit_6.text().strip()

    def run_sjhj_initial_check(self):
        """运行随机混剪初始化检查"""
        if not self.sjhj_files:
            QMessageBox.warning(self, "错误", "请先添加前贴文件！")
            return False

        if not self.sjhj_output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return False

        # 获取后贴素材库目录
        variant_folder = self.get_sjhj_variant_folder()
        if not variant_folder or not os.path.exists(variant_folder):
            QMessageBox.warning(self, "错误", f"后贴素材库目录不存在：{variant_folder}")
            return False

        # 获取后贴文件列表
        video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv']
        self.sjhj_variant_files = []

        for ext in video_extensions:
            self.sjhj_variant_files.extend(glob.glob(os.path.join(variant_folder, f"*{ext}")))
            self.sjhj_variant_files.extend(glob.glob(os.path.join(variant_folder, f"*{ext.upper()}")))

        if not self.sjhj_variant_files:
            QMessageBox.warning(self, "错误", f"后贴素材库中没有找到视频文件：{variant_folder}")
            return False

        # 检查前贴固定使用次数设置
        use_count = self.ui.SJHJ_spinBox_4.value()
        if use_count > len(self.sjhj_variant_files):
            QMessageBox.warning(
                self, "错误",
                f"前贴固定使用次数({use_count})超过了后贴库数量({len(self.sjhj_variant_files)})！\n"
                f"请修改设置或增加后贴素材。"
            )
            return False

        self.update_sjhj_log("开始初始化检查...")
        self.update_sjhj_log(f"前贴文件数量: {len(self.sjhj_files)}")
        self.update_sjhj_log(f"后贴素材库: {variant_folder}")
        self.update_sjhj_log(f"后贴文件数量: {len(self.sjhj_variant_files)}")
        self.update_sjhj_log(f"前贴固定使用次数: {use_count}")

        # 检查前贴文件的分辨率和帧率
        all_files = self.sjhj_files.copy()

        # 检查分辨率和帧率统一性
        resolutions = set()
        framerates = set()

        for file_path in all_files:
            try:
                resolution, framerate = self._get_video_info(file_path)
                if resolution and framerate:
                    resolutions.add(resolution)
                    framerates.add(framerate)
                else:
                    self.update_sjhj_log(f"⚠️ 无法获取视频信息: {os.path.basename(file_path)}")
            except Exception as e:
                self.update_sjhj_log(f"❌ 检查文件失败: {os.path.basename(file_path)} - {str(e)}")

        # 检查分辨率统一性
        if len(resolutions) > 1:
            self.update_sjhj_log("❌ 前贴文件分辨率不统一:")
            for res in resolutions:
                self.update_sjhj_log(f"   - {res}")
            QMessageBox.warning(self, "错误", "前贴文件分辨率不统一，请确保所有文件使用相同分辨率！")
            return False

        # 检查帧率统一性
        if len(framerates) > 1:
            self.update_sjhj_log("❌ 前贴文件帧率不统一:")
            for fps in framerates:
                self.update_sjhj_log(f"   - {fps} fps")
            QMessageBox.warning(self, "错误", "前贴文件帧率不统一，请确保所有文件使用相同帧率！")
            return False

        if resolutions and framerates:
            resolution = list(resolutions)[0]
            framerate = list(framerates)[0]
            self.update_sjhj_log(f"✅ 前贴文件格式统一: {resolution}, {framerate} fps")

        self.update_sjhj_log("✅ 初始化检查完成，可以开始混剪！")
        return True

    def update_sjhj_log(self, message):
        """更新随机混剪日志"""
        self.ui.SJHJCL_plainTextEdit_3.appendPlainText(message)

    def start_sjhj_mix(self):
        """开始随机混剪处理"""
        if not self.sjhj_files:
            QMessageBox.warning(self, "错误", "请先添加前贴文件！")
            return
        if not self.sjhj_output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 执行初始化检查
        if not self.run_sjhj_initial_check():
            return

        self.sjhj_is_running = True
        self.ui.SJHJKS_pushButton_3.setEnabled(False)
        self.ui.SJHJTZ_pushButton_3.setEnabled(True)
        self.ui.SJHJ_progressBar_3.setValue(0)

        self.update_sjhj_log("开始随机混剪处理...")

        # 启动随机混剪线程
        self.sjhj_thread = SJHJMixingThread(self)
        self.sjhj_thread.progress_updated.connect(self.ui.SJHJ_progressBar_3.setValue)
        self.sjhj_thread.log_updated.connect(self.update_sjhj_log)
        self.sjhj_thread.finished.connect(self.on_sjhj_mix_finished)
        self.sjhj_thread.start()

    def stop_sjhj_mix(self):
        """停止随机混剪处理"""
        if hasattr(self, 'sjhj_thread') and self.sjhj_thread.isRunning():
            self.sjhj_thread.stop()
            self.update_sjhj_log("用户停止了随机混剪处理")

    def on_sjhj_mix_finished(self):
        """随机混剪完成回调"""
        self.sjhj_is_running = False
        self.ui.SJHJKS_pushButton_3.setEnabled(True)
        self.ui.SJHJTZ_pushButton_3.setEnabled(False)
        self.update_sjhj_log("随机混剪处理完成！")


def main_application():
    """主应用程序函数"""
    app = QApplication(sys.argv)

    # 设置全局中文字体
    font = app.font()
    font.setFamily("SimHei")
    app.setFont(font)

    window = MainWindow()
    window.show()
    return app.exec()

def check_authorization_with_optimization():
    """优化的授权检查，保持完整验证但优化性能"""
    try:
        # 延迟导入授权模块，避免启动时加载重模块
        from machine_code_verifier import check_authorization_only

        # 进行完整的授权检查（包含时间验证）
        valid, _, _, _ = check_authorization_only("MFChen视频混剪工具")

        if valid:
            return True, None
        else:
            # 只有在需要时才导入GUI相关模块
            from machine_code_verifier import run_application_with_authorization_check
            return False, lambda: run_application_with_authorization_check(lambda: None, "MFChen视频混剪工具")
    except ImportError:
        # 开发环境，跳过授权检查
        return True, None

if __name__ == "__main__":
    # 优化的授权检查（保持完整验证）
    auth_valid, auth_func = check_authorization_with_optimization()

    if auth_valid:
        # 授权通过，直接启动主程序
        sys.exit(main_application())
    else:
        # 授权失败，显示授权对话框
        sys.exit(auth_func())


class SJHJMixingThread(QThread):
    """随机混剪处理线程"""
    progress_updated = Signal(int)
    log_updated = Signal(str)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.is_running = True

    def stop(self):
        """停止处理"""
        self.is_running = False

    def run(self):
        """执行随机混剪"""
        try:
            self._execute_sjhj_mixing()
        except Exception as e:
            self.log_updated.emit(f"❌ 随机混剪出错: {str(e)}")
        finally:
            self.is_running = False

    def _execute_sjhj_mixing(self):
        """执行随机混剪逻辑"""
        main_files = self.main_window.sjhj_files
        variant_files = self.main_window.sjhj_variant_files
        output_dir = self.main_window.sjhj_output_dir
        use_count = self.main_window.ui.SJHJ_spinBox_4.value()

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 计算总任务数
        total_tasks = len(main_files) * use_count
        current_task = 0

        # 创建后贴使用计数器，确保均匀使用
        variant_usage = {variant: 0 for variant in variant_files}

        self.log_updated.emit(f"开始处理 {len(main_files)} 个前贴文件，每个使用 {use_count} 次")

        # 为每个前贴文件生成指定次数的混剪
        for main_idx, main_file in enumerate(main_files):
            if not self.is_running:
                break

            main_name = os.path.splitext(os.path.basename(main_file))[0]

            # 为当前前贴选择不重复的后贴
            selected_variants = self._select_variants_for_main(variant_files, variant_usage, use_count)

            for use_idx, variant_file in enumerate(selected_variants):
                if not self.is_running:
                    break

                # 生成输出文件名
                variant_name = os.path.splitext(os.path.basename(variant_file))[0]
                output_filename = f"{main_name}_{use_idx+1}_{variant_name}.mp4"
                output_path = os.path.join(output_dir, output_filename)

                # 执行混剪
                self.log_updated.emit(f"正在处理: {output_filename}")
                success = self._mix_two_videos(main_file, variant_file, output_path)

                if success:
                    # 更新后贴使用计数
                    variant_usage[variant_file] += 1
                    self.log_updated.emit(f"✅ 完成: {output_filename}")
                else:
                    self.log_updated.emit(f"❌ 失败: {output_filename}")

                current_task += 1
                progress = int((current_task / total_tasks) * 100)
                self.progress_updated.emit(progress)

        if self.is_running:
            self.log_updated.emit("🎉 所有混剪任务完成！")

            # 如果启用了抽帧去重，执行抽帧处理
            if True:  # 强制开启抽帧去重
                self._execute_frame_removal(output_dir)

            # 如果启用了批量重命名，执行重命名
            if self.main_window.ui.CMM_checkBox_9.isChecked():
                self._execute_batch_rename(output_dir)

            # 清理临时文件
            self._cleanup_temp_files(output_dir)

    def _select_variants_for_main(self, variant_files, variant_usage, use_count):
        """为单个前贴选择不重复的后贴"""
        # 按使用次数排序，优先选择使用次数少的
        sorted_variants = sorted(variant_files, key=lambda x: variant_usage[x])

        # 选择前use_count个后贴
        return sorted_variants[:use_count]

    def _mix_two_videos(self, main_file, variant_file, output_path):
        """混剪两个视频"""
        try:
            # 复用tab_QT的混剪逻辑
            from video_mixer import VideoMixer

            # 创建临时混剪器
            mixer = VideoMixer()
            mixer.set_loudnorm_enabled(True)  # 启用响度统一

            # 设置转场配置
            if self.main_window.ui.SJHJZC_checkBox_2.isChecked():
                transition_type = self.main_window.ui.SJHJZC_comboBox_2.currentText()
                transition_duration = self.main_window.ui.SJHJZC_doubleSpinBox_3.value()
                mixer.transition_config = {
                    'enabled': True,
                    'type': transition_type,
                    'duration': transition_duration
                }

            # 执行混剪
            mixer._merge_two_videos(Path(main_file), Path(variant_file), Path(output_path), mixer.transition_config)

            return os.path.exists(output_path) and os.path.getsize(output_path) > 0

        except Exception as e:
            self.log_updated.emit(f"混剪出错: {str(e)}")
            return False

    def _execute_frame_removal(self, output_dir):
        """执行抽帧去重处理"""
        self.log_updated.emit("开始抽帧去重处理...")

        try:
            from frame_removal import FrameRemovalProcessor

            # 获取输出目录中的所有视频文件
            video_files = []
            for ext in ['.mp4', '.avi', '.mkv', '.mov']:
                video_files.extend(glob.glob(os.path.join(output_dir, f"*{ext}")))

            if not video_files:
                self.log_updated.emit("没有找到需要抽帧的视频文件")
                return

            # 创建抽帧处理器
            processor = FrameRemovalProcessor()
            processor.set_frame_range(15, 50)  # 固定最小15帧，最大50帧

            # 处理每个文件（替换模式）
            for i, video_file in enumerate(video_files):
                if not self.is_running:
                    break

                self.log_updated.emit(f"抽帧处理: {os.path.basename(video_file)}")

                # 创建临时输出文件
                temp_output = video_file + "_temp.mp4"

                try:
                    processor.process_single_file(video_file, temp_output, replace_mode=False)

                    # 替换原文件
                    if os.path.exists(temp_output) and os.path.getsize(temp_output) > 0:
                        os.replace(temp_output, video_file)
                        self.log_updated.emit(f"✅ 抽帧完成: {os.path.basename(video_file)}")
                    else:
                        self.log_updated.emit(f"❌ 抽帧失败: {os.path.basename(video_file)}")
                        if os.path.exists(temp_output):
                            os.remove(temp_output)

                except Exception as e:
                    self.log_updated.emit(f"❌ 抽帧出错: {os.path.basename(video_file)} - {str(e)}")
                    if os.path.exists(temp_output):
                        os.remove(temp_output)

            self.log_updated.emit("抽帧去重处理完成")

        except Exception as e:
            self.log_updated.emit(f"抽帧处理出错: {str(e)}")

    def _execute_batch_rename(self, output_dir):
        """执行批量重命名"""
        self.log_updated.emit("开始批量重命名...")

        try:
            # 获取重命名参数
            prefix = self.main_window.ui.CMM_lineEdit_8.text().strip()
            suffix = self.main_window.ui.CMM_lineEdit_7.text().strip()
            start_num = self.main_window.ui.CMM_spinBox_4.value()
            digits = self.main_window.ui.spinBox_4.value()
            keep_ext = self.main_window.ui.CMM_checkBox_8.isChecked()

            # 获取所有视频文件
            video_files = []
            for ext in ['.mp4', '.avi', '.mkv', '.mov']:
                video_files.extend(glob.glob(os.path.join(output_dir, f"*{ext}")))

            video_files.sort()  # 按文件名排序

            for i, old_path in enumerate(video_files):
                if not self.is_running:
                    break

                # 生成新文件名
                num_str = str(start_num + i).zfill(digits)

                if keep_ext:
                    ext = os.path.splitext(old_path)[1]
                    new_name = f"{prefix}{num_str}{suffix}{ext}"
                else:
                    new_name = f"{prefix}{num_str}{suffix}"

                new_path = os.path.join(output_dir, new_name)

                try:
                    os.rename(old_path, new_path)
                    self.log_updated.emit(f"重命名: {os.path.basename(old_path)} -> {new_name}")
                except Exception as e:
                    self.log_updated.emit(f"重命名失败: {os.path.basename(old_path)} - {str(e)}")

            self.log_updated.emit("批量重命名完成")

        except Exception as e:
            self.log_updated.emit(f"批量重命名出错: {str(e)}")

    def _cleanup_temp_files(self, output_dir):
        """清理临时文件（注意不要删除输出目录的原有文件）"""
        self.log_updated.emit("开始清理临时文件...")

        try:
            # 只清理特定的临时文件模式，避免误删用户文件
            temp_patterns = [
                "*_temp.mp4",
                "*_temp.avi",
                "*_temp.mkv",
                "*_temp.mov",
                "*.tmp",
                "*.temp"
            ]

            cleaned_count = 0
            for pattern in temp_patterns:
                temp_files = glob.glob(os.path.join(output_dir, pattern))
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                        cleaned_count += 1
                        self.log_updated.emit(f"清理临时文件: {os.path.basename(temp_file)}")
                    except Exception as e:
                        self.log_updated.emit(f"清理失败: {os.path.basename(temp_file)} - {str(e)}")

            if cleaned_count > 0:
                self.log_updated.emit(f"清理完成，共清理 {cleaned_count} 个临时文件")
            else:
                self.log_updated.emit("没有找到需要清理的临时文件")

        except Exception as e:
            self.log_updated.emit(f"清理临时文件出错: {str(e)}")

