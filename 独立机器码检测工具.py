#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立机器码检测工具
完全独立，无需任何外部依赖，专门用于诊断机器码变动问题
适用于刘若愚等用户的机器码稳定性检测
"""

import uuid
import hashlib
import json
import os
import platform
import subprocess
import sys
import time
from datetime import datetime

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

class IndependentMachineCodeChecker:
    def __init__(self):
        self.salt_key = "MFChen_Video_Tool_2025_Secret_Key_MDZNB&*"
        
    def print_separator(self, title="", char="=", width=60):
        """打印分隔线"""
        if title:
            title_line = f" {title} "
            padding = (width - len(title_line)) // 2
            line = char * padding + title_line + char * padding
            if len(line) < width:
                line += char
        else:
            line = char * width
        print(line)

    def print_status(self, status, message):
        """打印状态信息"""
        status_icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "question": "❓"
        }
        icon = status_icons.get(status, "•")
        print(f"{icon} {message}")

    def get_hardware_components(self):
        """获取硬件组件信息"""
        components = {}
        
        if os.name == "nt":  # Windows
            # 主板序列号
            try:
                result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            components["motherboard_serial"] = line.strip()
                            break
            except:
                components["motherboard_serial"] = "unknown"
            
            # CPU ID
            try:
                result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            components["cpu_id"] = line.strip()
                            break
            except:
                components["cpu_id"] = "unknown"
            
            # 硬盘信息
            try:
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber,model,size'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    disk_info = []
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.strip().split()
                            if parts:
                                disk_info.append({
                                    "serial": parts[0] if len(parts) > 0 else "unknown",
                                    "model": parts[1] if len(parts) > 1 else "unknown",
                                    "size": parts[2] if len(parts) > 2 else "unknown"
                                })
                    components["disk_info"] = disk_info
                    components["disk_serials"] = [d["serial"] for d in disk_info]
                    components["first_disk_serial"] = disk_info[0]["serial"] if disk_info else "unknown"
                    components["disk_count"] = len(disk_info)
            except:
                components["disk_info"] = []
                components["disk_serials"] = []
                components["first_disk_serial"] = "unknown"
                components["disk_count"] = 0
        
        else:  # Linux/Mac
            # 机器ID
            for path in ['/etc/machine-id', '/var/lib/dbus/machine-id']:
                try:
                    if os.path.exists(path):
                        with open(path, 'r') as f:
                            components[f"machine_id_{path.split('/')[-1]}"] = f.read().strip()
                except:
                    pass
        
        # MAC地址
        try:
            mac = uuid.getnode()
            components["mac_address"] = f"{mac:012x}"
        except:
            components["mac_address"] = "unknown"
        
        return components

    def generate_machine_code(self):
        """生成机器码"""
        if os.name == "nt":  # Windows
            try:
                # 主板序列号
                result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                motherboard_serial = ""
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            motherboard_serial = line.strip()
                            break

                # CPU ID
                result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                cpu_id = ""
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            cpu_id = line.strip()
                            break

                # 第一个硬盘序列号
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=CREATE_NO_WINDOW)
                disk_serial = ""
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            disk_serial = line.strip()
                            break

                # 组合硬件信息
                hardware_info = f"{motherboard_serial}_{cpu_id}_{disk_serial}"
                
            except Exception:
                # 如果获取硬件信息失败，使用MAC地址作为备用
                mac = uuid.getnode()
                hardware_info = f"mac_{mac:012x}"
        
        else:  # Linux/Mac
            try:
                # 尝试读取机器ID
                machine_id = ""
                for path in ['/etc/machine-id', '/var/lib/dbus/machine-id']:
                    try:
                        if os.path.exists(path):
                            with open(path, 'r') as f:
                                machine_id = f.read().strip()
                                break
                    except:
                        continue
                
                if machine_id:
                    hardware_info = f"machine_id_{machine_id}"
                else:
                    # 如果没有机器ID，使用MAC地址
                    mac = uuid.getnode()
                    hardware_info = f"mac_{mac:012x}"
                    
            except Exception:
                # 最后的备用方案
                mac = uuid.getnode()
                hardware_info = f"mac_{mac:012x}"
        
        # 生成机器码
        machine_code = hashlib.md5(hardware_info.encode()).hexdigest()
        return machine_code, hardware_info

    def convert_to_md5_with_salt(self, machine_code):
        """使用盐值转换机器码为MD5"""
        combined = f"{machine_code}_{self.salt_key}_{machine_code[:8]}"
        md5_hash = hashlib.md5(combined.encode()).hexdigest()
        return md5_hash.upper()

    def quick_check(self):
        """快速检查当前机器码"""
        self.print_separator("机器码快速检查")
        self.print_status("info", "正在获取机器硬件信息...")
        
        try:
            # 获取当前机器码信息
            components = self.get_hardware_components()
            machine_code, hardware_string = self.generate_machine_code()
            md5_code = self.convert_to_md5_with_salt(machine_code)
            
            self.print_separator("当前机器信息")
            print(f"机器码: {machine_code}")
            print(f"MD5码: {md5_code}")
            print(f"硬件字符串: {hardware_string}")
            print()
            
            self.print_separator("硬件组件详情")
            print(f"主板序列号: {components.get('motherboard_serial', 'Unknown')}")
            print(f"CPU ID: {components.get('cpu_id', 'Unknown')}")
            print(f"MAC地址: {components.get('mac_address', 'Unknown')}")
            print(f"硬盘数量: {components.get('disk_count', 0)}")
            
            disk_info = components.get('disk_info', [])
            if disk_info:
                print("硬盘详情:")
                for i, disk in enumerate(disk_info, 1):
                    serial = disk.get('serial', 'Unknown')
                    model = disk.get('model', 'Unknown')
                    size = disk.get('size', 'Unknown')
                    print(f"  {i}. {serial} ({model}, {size})")
            print()
            
            return True, {
                "machine_code": machine_code,
                "md5_code": md5_code,
                "hardware_string": hardware_string,
                "components": components
            }
            
        except Exception as e:
            self.print_status("error", f"检查失败: {str(e)}")
            return False, None

    def stability_test(self, iterations=5, interval=3):
        """稳定性测试"""
        self.print_separator("机器码稳定性测试")
        self.print_status("info", f"将进行 {iterations} 次检测，间隔 {interval} 秒")
        self.print_status("warning", "请在测试期间不要插拔USB设备或重启电脑")
        print()

        try:
            results = []

            for i in range(iterations):
                print(f"📊 第 {i+1}/{iterations} 次检测:")

                # 获取机器码信息
                components = self.get_hardware_components()
                machine_code, hardware_string = self.generate_machine_code()
                md5_code = self.convert_to_md5_with_salt(machine_code)

                result = {
                    "iteration": i + 1,
                    "machine_code": machine_code,
                    "md5_code": md5_code,
                    "hardware_string": hardware_string,
                    "disk_count": components.get('disk_count', 0),
                    "first_disk": components.get('first_disk_serial', 'unknown'),
                    "timestamp": datetime.now().isoformat()
                }
                results.append(result)

                print(f"  机器码: {machine_code}")
                print(f"  MD5码: {md5_code}")
                print(f"  硬盘数量: {result['disk_count']}")
                print(f"  第一硬盘: {result['first_disk']}")

                # 检查是否有变动
                if len(results) > 1:
                    prev_code = results[-2]["machine_code"]
                    if machine_code != prev_code:
                        self.print_status("error", "⚠️ 检测到机器码变动！")
                        print(f"    上次: {prev_code}")
                        print(f"    本次: {machine_code}")

                if i < iterations - 1:
                    print(f"  等待 {interval} 秒...")
                    time.sleep(interval)
                print()

            # 分析结果
            self.print_separator("稳定性分析结果")

            machine_codes = [r["machine_code"] for r in results]
            md5_codes = [r["md5_code"] for r in results]
            first_disks = [r["first_disk"] for r in results]
            disk_counts = [r["disk_count"] for r in results]

            unique_machine_codes = set(machine_codes)
            unique_md5_codes = set(md5_codes)
            unique_first_disks = set(first_disks)
            unique_disk_counts = set(disk_counts)

            print(f"检测次数: {len(results)}")
            print(f"唯一机器码数量: {len(unique_machine_codes)}")
            print(f"唯一MD5码数量: {len(unique_md5_codes)}")
            print(f"硬盘数量变化: {len(unique_disk_counts)} 种")
            print(f"第一硬盘变化: {len(unique_first_disks)} 种")
            print()

            # 判断稳定性
            is_stable = len(unique_machine_codes) == 1

            if is_stable:
                self.print_status("success", "机器码完全稳定")
                self.print_status("info", "无需担心机器码变动问题")
                print()
                self.print_status("info", "当前机器码信息:")
                print(f"  机器码: {machine_codes[0]}")
                print(f"  MD5码: {md5_codes[0]}")
                print(f"  硬盘数量: {disk_counts[0]}")
                print(f"  第一硬盘: {first_disks[0]}")
            else:
                self.print_status("error", "检测到机器码不稳定！")
                self.print_status("warning", "发现以下变动:")

                if len(unique_machine_codes) > 1:
                    print("  • 机器码发生变化:")
                    for code in unique_machine_codes:
                        count = machine_codes.count(code)
                        print(f"    - {code} (出现{count}次)")

                if len(unique_first_disks) > 1:
                    print("  • 第一硬盘序列号发生变化:")
                    for disk in unique_first_disks:
                        count = first_disks.count(disk)
                        print(f"    - {disk} (出现{count}次)")

                if len(unique_disk_counts) > 1:
                    print("  • 硬盘数量发生变化:")
                    for count in unique_disk_counts:
                        occurrences = disk_counts.count(count)
                        print(f"    - {count}个硬盘 (出现{occurrences}次)")

                print()
                self.print_separator("问题诊断和解决建议")
                self.print_status("warning", "可能的原因:")
                print("  1. 硬盘连接松动或不稳定")
                print("  2. BIOS中硬盘启动顺序不固定")
                print("  3. USB设备插拔影响硬盘枚举顺序")
                print("  4. 虚拟硬盘或网络硬盘干扰")
                print("  5. 硬盘驱动程序问题")
                print()

                self.print_status("info", "建议解决方案:")
                print("  1. 检查所有硬盘的SATA线和电源线连接")
                print("  2. 进入BIOS设置，固定硬盘启动顺序")
                print("  3. 在使用软件时避免插拔USB设备")
                print("  4. 关闭虚拟机软件和网络硬盘")
                print("  5. 更新硬盘驱动程序")
                print("  6. 重启电脑后重新测试")

            # 保存详细报告
            self.save_report(results, is_stable)

            print()
            return is_stable, results

        except Exception as e:
            self.print_status("error", f"稳定性测试失败: {str(e)}")
            return False, []

    def save_report(self, results, is_stable):
        """保存详细报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"机器码检测报告_{timestamp}.json"

            report = {
                "检测时间": datetime.now().isoformat(),
                "系统信息": {
                    "操作系统": platform.system(),
                    "系统版本": platform.release(),
                    "平台": platform.platform(),
                    "处理器": platform.processor()
                },
                "稳定性": "稳定" if is_stable else "不稳定",
                "检测结果": results,
                "统计信息": {
                    "总检测次数": len(results),
                    "唯一机器码数量": len(set(r["machine_code"] for r in results)),
                    "唯一MD5码数量": len(set(r["md5_code"] for r in results)),
                    "硬盘数量变化": len(set(r["disk_count"] for r in results)),
                    "第一硬盘变化": len(set(r["first_disk"] for r in results))
                }
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.print_status("success", f"详细报告已保存: {filename}")

        except Exception as e:
            self.print_status("warning", f"保存报告失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 独立机器码检测工具")
    print("专门用于诊断机器码变动问题")
    print("适用于多硬盘环境的稳定性检测")
    print()

    checker = IndependentMachineCodeChecker()

    try:
        # 显示当前系统信息
        print(f"系统: {platform.system()} {platform.release()}")
        print(f"平台: {platform.platform()}")
        print()

        # 快速检查
        success, info = checker.quick_check()

        if not success:
            print("快速检查失败，程序退出")
            input("按回车键退出...")
            return

        # 询问是否进行稳定性测试
        checker.print_status("question", "是否进行稳定性测试？")
        print("稳定性测试将检测机器码是否会发生变动")
        print("建议进行测试以确保机器码稳定性")
        print()

        while True:
            choice = input("请选择 (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是', '1']:
                print()

                # 询问检测参数
                print("请设置检测参数:")
                try:
                    iterations = input("检测次数 (默认5次): ").strip()
                    iterations = int(iterations) if iterations else 5
                    iterations = max(1, min(20, iterations))  # 限制在1-20次

                    interval = input("检测间隔秒数 (默认3秒): ").strip()
                    interval = int(interval) if interval else 3
                    interval = max(1, min(60, interval))  # 限制在1-60秒

                except ValueError:
                    print("使用默认参数: 5次检测，间隔3秒")
                    iterations = 5
                    interval = 3

                print()
                is_stable, results = checker.stability_test(iterations, interval)

                if not is_stable:
                    checker.print_status("warning", "检测到机器码不稳定！")
                    checker.print_status("info", "请按照上述建议解决问题后重新测试")

                break

            elif choice in ['n', 'no', '否', '0']:
                checker.print_status("info", "跳过稳定性测试")
                break
            else:
                print("请输入 y 或 n")

        print()
        checker.print_status("info", "检测完成！")

        if 'info' in locals():
            print()
            checker.print_separator("最终结果")
            print(f"当前机器码: {info['machine_code']}")
            print(f"当前MD5码: {info['md5_code']}")

    except KeyboardInterrupt:
        print()
        checker.print_status("warning", "用户中断操作")
    except Exception as e:
        print()
        checker.print_status("error", f"程序运行出错: {str(e)}")

    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
