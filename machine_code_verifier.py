import uuid
import hashlib
import json
import os
import platform
import subprocess
import sys
import time
from datetime import datetime, timedelta

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

# 尝试导入PySide6，如果失败则设置标志
try:
    from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QTextEdit
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    # 创建虚拟类以避免错误
    class QWidget: pass
    class QApplication: pass

class MachineCodeVerifier:
    def __init__(self, app_name="YourApp"):
        self.app_name = app_name
        self.machine_id = self._get_machine_id()

        # 密钥盐值 - 用于机器码转MD5（与转换工具保持一致）
        self.salt_key = "MFChen_Video_Tool_2025_Secret_Key_MDZNB&*"

        # 预设的授权配置 - 包含MD5值和过期时间
        self.authorized_config = {
            "F168DDA7BD43EF480A7A2A2782F2D248": {
                "expire_date": "2030-06-06",  # 过期日期 YYYY-MM-DD
                "description": "当前测试机器"
            },
            "BA00AECDB6DF2324140344BD04908D62": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器"
            },
            "B3C8A19C0C714D04E2CFBF74C5AAAB60": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器2"
            },
            "4128B2049362134C1C8C35DE3E754198": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器3"
            },
            "BE534607DC3F30F044AA69EF163B8B74": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器4"
            },
            "991EF0C855F11CF6D02BEB686CC7AB12": {
                "expire_date": "2030-06-06",
                "description": "李家康的机器"
            },
            "2E0E078C1215BE2F84A421278839652D": {
                "expire_date": "2030-06-06",
                "description": "张鹏程（真白）的机器"
            },
            "BABA53F3F902AF372C837F934325C0C8": {
                "expire_date": "2030-06-06",
                "description": "张鹏程（真白）的机器2"
            },
            "CE19E6F573FD457B968F05B37B710E71": {
                "expire_date": "2030-06-06",
                "description": "罗慧（实实）的机器"
            },
            "A5F42F3D8AC92549DCAED0E42057420F": {
                "expire_date": "2030-06-06",
                "description": "罗慧（实实）的机器2"
            },
            "1B034810DD2DCEC460D7B6DE61744902": {
                "expire_date": "2030-06-06",
                "description": "李建萌的机器"
            },
            # 可以添加更多授权配置
        }

        # 延迟初始化时间验证器
        self.time_validator = None
        self.time_validation_enabled = None  # 延迟检查

    def _get_machine_id(self):
        """生成基于多种硬件信息的稳定唯一机器码"""
        machine_info = []

        try:
            # 获取主板序列号
            if os.name == "nt":  # Windows
                try:
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取CPU序列号
                try:
                    result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'ProcessorId' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取硬盘序列号
                try:
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line and line.strip() != '(null)':
                                machine_info.append(line.strip())
                                break  # 只取第一个硬盘
                except:
                    pass

            elif os.name == "posix":  # Linux/Mac
                try:
                    # 尝试获取机器ID
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                    elif os.path.exists('/var/lib/dbus/machine-id'):
                        with open('/var/lib/dbus/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                except:
                    pass

                # 获取CPU信息
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'serial' in line.lower():
                                machine_info.append(line.split(':')[1].strip())
                                break
                except:
                    pass
        except:
            pass

        # 获取MAC地址作为备用
        try:
            mac = uuid.getnode()
            machine_info.append(str(mac))
        except:
            pass

        # 获取系统信息作为额外标识
        try:
            machine_info.append(platform.machine())
            machine_info.append(platform.processor())
        except:
            pass

        # 如果没有获取到任何信息，使用UUID作为备用
        if not machine_info:
            machine_info.append(str(uuid.uuid4()))

        # 组合所有信息并生成哈希
        combined_info = ''.join(machine_info)
        return hashlib.sha256(combined_info.encode()).hexdigest()[:32]  # 取前32位

    def _init_time_validator(self):
        """延迟初始化时间验证器"""
        if self.time_validation_enabled is None:
            try:
                from time_validator import TimeValidator
                self.time_validator = TimeValidator()
                self.time_validation_enabled = True
            except ImportError:
                self.time_validator = None
                self.time_validation_enabled = False

    def _convert_machine_code_to_md5(self, machine_code):
        """
        将机器码转换为独特的MD5值（与转换工具保持一致）
        """
        if not machine_code:
            return ""

        # 第一步：机器码 + 盐值
        step1 = machine_code + self.salt_key

        # 第二步：反转字符串
        step2 = step1[::-1]

        # 第三步：插入额外字符
        step3 = ""
        for i, char in enumerate(step2):
            step3 += char
            if i % 3 == 0:
                step3 += str(i % 10)

        # 第四步：再次加盐并生成MD5
        final_string = step3 + self.salt_key + machine_code
        md5_hash = hashlib.md5(final_string.encode('utf-8')).hexdigest()

        return md5_hash.upper()  # 返回大写MD5

    def _date_to_timestamp(self, date_str):
        """将日期字符串转换为时间戳"""
        try:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            return int(dt.timestamp())
        except Exception:
            return None

    def is_machine_authorized(self):
        """检查当前机器是否在授权列表中"""
        current_md5 = self._convert_machine_code_to_md5(self.machine_id)
        return current_md5 in self.authorized_config

    def get_machine_code(self):
        """获取当前机器的机器码"""
        return self.machine_id

    def get_machine_md5(self):
        """获取当前机器的MD5值"""
        return self._convert_machine_code_to_md5(self.machine_id)

    def verify_authorization(self):
        """验证机器授权（包含时间检查）"""
        current_md5 = self._convert_machine_code_to_md5(self.machine_id)

        # 1. 检查MD5是否在授权列表中
        if current_md5 not in self.authorized_config:
            return False, f"未授权的机器\n当前机器码: {self.machine_id}\n请联系管理员获取授权"

        # 2. 获取授权配置
        config = self.authorized_config[current_md5]
        expire_date = config.get("expire_date")
        description = config.get("description", "")

        # 3. 时间验证（延迟初始化）
        if expire_date:
            self._init_time_validator()
            if self.time_validation_enabled:
                expire_timestamp = self._date_to_timestamp(expire_date)
                if expire_timestamp:
                    time_valid, time_message = self.time_validator.validate_authorization_time(expire_timestamp)
                    if not time_valid:
                        return False, time_message

                    # 检查是否即将过期（7天内）
                    current_time = int(time.time())
                    days_left = (expire_timestamp - current_time) / (24 * 3600)
                    if days_left <= 7:
                        return True, f"授权验证通过（{description}）\n⚠️ 注意：授权将在{days_left:.0f}天后过期（{expire_date}）"

        return True, f"授权验证通过（{description}）"

class AuthorizationDialog(QWidget):
    def __init__(self, verifier):
        super().__init__()
        self.verifier = verifier
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 显示当前机器码
        machine_code_label = QLabel(f"当前机器码: {self.verifier.machine_id}")
        machine_code_label.setWordWrap(True)
        layout.addWidget(machine_code_label)

        # 显示授权状态
        valid, message = self.verifier.verify_authorization()
        status_label = QLabel(message)
        status_label.setWordWrap(True)
        layout.addWidget(status_label)

        # 如果未授权，显示联系信息
        if not valid:
            contact_label = QLabel("请将上述机器码发送给管理员以获取授权")
            contact_label.setWordWrap(True)
            layout.addWidget(contact_label)

        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.close)
        layout.addWidget(ok_button)

        self.setLayout(layout)
        self.setWindowTitle("软件授权验证")
        self.setGeometry(300, 300, 400, 200)
        self.setStyleSheet("QLabel { margin: 10px; } QPushButton { margin: 5px; }")

def run_application_with_authorization_check(main_app_func, app_name="YourApp"):
    """
    运行带有机器码授权检查的应用程序

    参数:
    main_app_func: 主应用程序入口函数
    app_name: 应用程序名称
    """
    verifier = MachineCodeVerifier(app_name)

    # 检查机器授权
    valid, message = verifier.verify_authorization()
    if valid:
        # 授权通过，运行主应用程序
        main_app_func()
        return True
    else:
        # 未授权处理
        if PYSIDE6_AVAILABLE:
            # 如果有PySide6，显示GUI对话框
            app = QApplication([])
            auth_dialog = AuthorizationDialog(verifier)
            auth_dialog.show()
            return app.exec()
        else:
            # 如果没有PySide6，打印错误信息
            print("=" * 50)
            print("软件授权验证失败")
            print("=" * 50)
            print(message)
            print("=" * 50)
            return False

def check_authorization_only(app_name="YourApp"):
    """
    仅检查授权状态，不启动GUI（用于命令行或测试）

    返回: (是否授权, 消息, 机器码, MD5值)
    """
    verifier = MachineCodeVerifier(app_name)
    valid, message = verifier.verify_authorization()
    return valid, message, verifier.machine_id, verifier.get_machine_md5()



# 使用示例
if __name__ == "__main__":
    # 定义你的主应用程序函数
    def main_application():
        # 这里是你的主应用程序代码
        from PySide6.QtWidgets import QMainWindow, QLabel

        window = QMainWindow()
        window.setWindowTitle("我的应用")
        window.setGeometry(300, 300, 400, 200)

        label = QLabel("欢迎使用我的应用！")
        window.setCentralWidget(label)
        window.show()

    # 运行带有机器码授权检查的应用
    run_application_with_authorization_check(main_application, "MyApp")
