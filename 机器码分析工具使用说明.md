# 机器码分析工具使用说明

## 📋 工具概述

为了解决刘若愚机器码变动问题，我创建了三个专门的分析工具：

1. **machine_code_analyzer.py** - 核心分析引擎
2. **machine_code_gui_analyzer.py** - 图形界面分析工具（推荐）
3. **quick_machine_code_check.py** - 快速命令行检查工具

## 🎯 问题背景

刘若愚的机器在授权系统中出现了4个不同的机器码，但实际只有一台机器。可能原因：
- 多硬盘环境下硬盘顺序变化
- 硬盘连接不稳定
- BIOS设置变动
- USB设备影响硬盘枚举

## 🔧 工具功能

### 1. GUI分析工具（推荐使用）

**文件**: `machine_code_gui_analyzer.py`

**功能特性**:
- 🖥️ 直观的图形界面
- 📊 实时检测结果表格
- 📈 稳定性分析报告
- 🔧 详细硬件信息显示
- 💾 分析报告保存功能
- ⚠️ 变动高亮显示

**使用方法**:
```bash
python machine_code_gui_analyzer.py
```

**操作步骤**:
1. 设置检测次数（建议5-10次）
2. 设置检测间隔（建议2-5秒）
3. 点击"🚀 开始分析"
4. 观察检测结果和稳定性分析
5. 保存分析报告

### 2. 快速命令行工具

**文件**: `quick_machine_code_check.py`

**功能特性**:
- ⚡ 快速检查当前机器码
- 🔍 硬件组件详情显示
- ✅ 授权状态检查
- 📊 可选稳定性测试

**使用方法**:
```bash
# 快速检查
python quick_machine_code_check.py

# 稳定性测试（10次检测，间隔3秒）
python quick_machine_code_check.py -s 10 3

# 显示帮助
python quick_machine_code_check.py --help
```

### 3. 核心分析引擎

**文件**: `machine_code_analyzer.py`

**功能特性**:
- 🔬 详细硬件信息获取
- 🧮 机器码生成算法
- 📝 JSON格式报告输出
- 🔄 多次检测稳定性分析

**使用方法**:
```bash
python machine_code_analyzer.py
```

## 📊 分析结果解读

### 稳定性状态

- **✅ 机器码完全稳定**: 所有检测中机器码完全一致
- **❌ 机器码不稳定**: 检测到机器码变动

### 关键指标

- **唯一机器码数量**: 应该为1，如果>1说明有变动
- **硬盘数量变化**: 硬盘数量是否发生变化
- **第一硬盘变化**: 第一个硬盘序列号是否变化（关键因素）

### 变动原因分析

如果检测到机器码不稳定，工具会分析可能原因：

1. **硬盘顺序变化** - 最常见原因
2. **硬盘连接问题** - 硬盘时有时无
3. **USB设备影响** - 插拔USB设备改变硬盘顺序
4. **虚拟硬盘干扰** - 虚拟机或网络硬盘

## 🛠️ 解决方案建议

### 对于刘若愚的情况

1. **运行GUI分析工具**:
   ```bash
   python machine_code_gui_analyzer.py
   ```
   - 设置检测次数: 10次
   - 设置间隔: 5秒
   - 观察是否有机器码变动

2. **如果检测到变动**:
   - 检查硬盘连接是否松动
   - 进入BIOS固定硬盘启动顺序
   - 避免在使用软件时插拔USB设备
   - 检查是否有虚拟硬盘软件运行

3. **如果没有检测到变动**:
   - 可能是历史遗留问题
   - 建议重新生成授权记录
   - 删除旧的重复机器码

### 通用解决方案

1. **硬件检查**:
   - 确保所有硬盘连接牢固
   - 检查SATA线和电源线
   - 确认硬盘健康状态

2. **BIOS设置**:
   - 固定硬盘启动顺序
   - 禁用不必要的存储设备
   - 确保设置保存正确

3. **软件环境**:
   - 避免使用虚拟硬盘软件
   - 减少USB设备的插拔
   - 定期检查机器码稳定性

## 📁 输出文件说明

### JSON报告格式

工具会生成详细的JSON报告，包含：

```json
{
  "analysis_time": "分析时间",
  "system_info": {
    "platform": "系统平台信息",
    "system": "操作系统",
    "machine": "机器类型",
    "processor": "处理器信息"
  },
  "results": [
    {
      "iteration": "检测次数",
      "timestamp": "检测时间",
      "machine_code": "机器码",
      "md5_code": "MD5码",
      "hardware_string": "硬件字符串",
      "components": {
        "motherboard_serial": "主板序列号",
        "cpu_id": "CPU ID",
        "disk_count": "硬盘数量",
        "disk_info": "硬盘详细信息"
      }
    }
  ],
  "summary": {
    "total_tests": "总检测次数",
    "unique_machine_codes": "唯一机器码数量",
    "stable": "是否稳定"
  }
}
```

## 🚀 使用建议

### 给刘若愚的建议

1. **立即运行GUI工具进行诊断**
2. **如果发现变动，按照建议解决方案操作**
3. **解决后重新运行确认稳定性**
4. **将分析报告发送给管理员**

### 给其他用户的建议

1. **定期运行快速检查工具**
2. **在硬件变更后检查机器码稳定性**
3. **保存分析报告作为问题诊断依据**

## ❓ 常见问题

**Q: 为什么需要多次检测？**
A: 机器码变动可能是间歇性的，多次检测能更准确地发现问题。

**Q: 检测间隔设置多少合适？**
A: 建议2-5秒，太短可能检测不到变动，太长会增加等待时间。

**Q: 如何确定问题已解决？**
A: 连续多次检测（建议10次以上）都显示机器码稳定。

**Q: 工具是否会影响系统性能？**
A: 不会，工具只读取硬件信息，不会修改任何系统设置。

## 📞 技术支持

如果使用过程中遇到问题，请提供：
1. 分析报告JSON文件
2. 系统配置信息
3. 具体错误信息

这些工具将帮助快速诊断和解决机器码变动问题！
